#include "../include/BSTFaceUnlock.h"
#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <string>
#include <filesystem>
#include <fstream>
#include <chrono>
#include <iomanip>

using namespace cv;
using namespace std;
using namespace std::chrono;

/**
 * 人脸对齐数据生成器
 * 基于BSTFaceUnlock类生成对齐后的人脸数据集
 */
class AlignDataMaker {
private:
    BSTFaceUnlock face_unlock;
    string input_dir;
    string output_dir;
    string predictor_path;
    bool initialized;
    
    // 统计信息
    struct Statistics {
        int total_images = 0;
        int processed_images = 0;
        int faces_detected = 0;
        int faces_aligned = 0;
        int failed_images = 0;
        double total_time = 0.0;
    } stats;
    
public:
    AlignDataMaker(const string& input_path, const string& output_path, 
                   const string& predictor = "") 
        : input_dir(input_path), output_dir(output_path), 
          predictor_path(predictor), initialized(false) {
        
        // 创建输出目录
        filesystem::create_directories(output_dir);
        filesystem::create_directories(output_dir + "/aligned");
        filesystem::create_directories(output_dir + "/annotated");
    }
    
    bool initialize() {
        cout << "初始化人脸对齐数据生成器..." << endl;
        
        if (!predictor_path.empty()) {
            if (face_unlock.initialize(predictor_path)) {
                cout << "成功加载预测器: " << predictor_path << endl;
                initialized = true;
            } else {
                cout << "警告: 无法加载预测器，将使用基本对齐方法" << endl;
                initialized = true; // 仍然可以工作，只是精度较低
            }
        } else {
            cout << "未指定预测器路径，使用基本对齐方法" << endl;
            initialized = true;
        }
        
        return initialized;
    }
    
    vector<string> getImageFiles() {
        vector<string> image_files;
        vector<string> extensions = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif"};
        
        if (!filesystem::exists(input_dir)) {
            cout << "错误: 输入目录不存在: " << input_dir << endl;
            return image_files;
        }
        
        for (const auto& entry : filesystem::recursive_directory_iterator(input_dir)) {
            if (entry.is_regular_file()) {
                string ext = entry.path().extension().string();
                transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
                
                if (find(extensions.begin(), extensions.end(), ext) != extensions.end()) {
                    image_files.push_back(entry.path().string());
                }
            }
        }
        
        sort(image_files.begin(), image_files.end());
        return image_files;
    }
    
    bool processImage(const string& image_path) {
        Mat image = imread(image_path);
        if (image.empty()) {
            cout << "无法读取图像: " << image_path << endl;
            return false;
        }
        
        string filename = filesystem::path(image_path).stem().string();
        
        // 检测人脸
        vector<Rect> faces = face_unlock.detectFaces(image);
        if (faces.empty()) {
            cout << "未检测到人脸: " << filename << endl;
            return false;
        }
        
        stats.faces_detected += faces.size();
        
        // 创建标注图像
        Mat annotated_image = image.clone();
        bool has_aligned_face = false;
        
        // 处理每个检测到的人脸
        for (size_t i = 0; i < faces.size(); ++i) {
            const Rect& face = faces[i];
            
            // 绘制人脸框
            rectangle(annotated_image, face, Scalar(0, 255, 0), 2);
            
            // 添加人脸编号
            string face_label = "Face " + to_string(i + 1);
            putText(annotated_image, face_label, 
                   Point(face.x, face.y - 10), 
                   FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0, 255, 0), 2);
            
            // 检测关键点
            vector<Point2f> landmarks = face_unlock.detectLandmarks(image, face);
            
            // 绘制关键点
            for (const auto& point : landmarks) {
                circle(annotated_image, point, 2, Scalar(0, 0, 255), -1);
            }
            
            // 人脸对齐
            if (!landmarks.empty()) {
                Mat aligned_face = face_unlock.alignFace(image, landmarks);
                
                if (!aligned_face.empty()) {
                    string aligned_filename = filename + "_face_" + to_string(i) + "_aligned.jpg";
                    string aligned_path = output_dir + "/aligned/" + aligned_filename;
                    
                    if (imwrite(aligned_path, aligned_face)) {
                        has_aligned_face = true;
                        stats.faces_aligned++;
                        cout << "  保存对齐人脸: " << aligned_filename << endl;
                    }
                }
            }
        }
        
        // 保存标注图像
        string annotated_filename = filename + "_annotated.jpg";
        string annotated_path = output_dir + "/annotated/" + annotated_filename;
        imwrite(annotated_path, annotated_image);
        
        return has_aligned_face;
    }
    
    void generateDataset() {
        if (!initialized) {
            cout << "错误: 未初始化" << endl;
            return;
        }
        
        cout << "开始生成人脸对齐数据集..." << endl;
        cout << "输入目录: " << input_dir << endl;
        cout << "输出目录: " << output_dir << endl;
        cout << "================================" << endl;
        
        // 获取所有图像文件
        vector<string> image_files = getImageFiles();
        if (image_files.empty()) {
            cout << "未找到图像文件" << endl;
            return;
        }
        
        stats.total_images = image_files.size();
        cout << "找到 " << stats.total_images << " 个图像文件" << endl;
        
        auto start_time = high_resolution_clock::now();
        
        // 处理每个图像
        for (size_t i = 0; i < image_files.size(); ++i) {
            const string& image_path = image_files[i];
            string filename = filesystem::path(image_path).filename().string();
            
            cout << "处理 [" << (i + 1) << "/" << stats.total_images << "] " << filename << endl;
            
            if (processImage(image_path)) {
                stats.processed_images++;
            } else {
                stats.failed_images++;
            }
        }
        
        auto end_time = high_resolution_clock::now();
        stats.total_time = duration_cast<milliseconds>(end_time - start_time).count() / 1000.0;
        
        // 生成处理报告
        generateReport();
        
        cout << "\n数据集生成完成!" << endl;
        printStatistics();
    }
    
    void generateReport() {
        string report_path = output_dir + "/processing_report.txt";
        ofstream report(report_path);
        
        if (!report.is_open()) {
            cout << "无法创建报告文件: " << report_path << endl;
            return;
        }
        
        auto now = system_clock::now();
        auto time_t = system_clock::to_time_t(now);
        
        report << "人脸对齐数据集生成报告" << endl;
        report << "========================" << endl;
        report << "生成时间: " << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S") << endl;
        report << "输入目录: " << input_dir << endl;
        report << "输出目录: " << output_dir << endl;
        report << "预测器路径: " << (predictor_path.empty() ? "未使用" : predictor_path) << endl;
        report << endl;
        
        report << "处理统计:" << endl;
        report << "  总图像数: " << stats.total_images << endl;
        report << "  成功处理: " << stats.processed_images << endl;
        report << "  失败图像: " << stats.failed_images << endl;
        report << "  检测人脸: " << stats.faces_detected << endl;
        report << "  对齐人脸: " << stats.faces_aligned << endl;
        report << "  处理时间: " << fixed << setprecision(2) << stats.total_time << " 秒" << endl;
        
        if (stats.total_images > 0) {
            report << "  成功率: " << fixed << setprecision(1) 
                   << (100.0 * stats.processed_images / stats.total_images) << "%" << endl;
            report << "  平均处理时间: " << fixed << setprecision(3)
                   << (stats.total_time / stats.total_images) << " 秒/图像" << endl;
        }
        
        report.close();
        cout << "处理报告已保存: " << report_path << endl;
    }
    
    void printStatistics() {
        cout << "\n处理统计:" << endl;
        cout << "  总图像数: " << stats.total_images << endl;
        cout << "  成功处理: " << stats.processed_images << endl;
        cout << "  失败图像: " << stats.failed_images << endl;
        cout << "  检测人脸: " << stats.faces_detected << endl;
        cout << "  对齐人脸: " << stats.faces_aligned << endl;
        cout << "  处理时间: " << fixed << setprecision(2) << stats.total_time << " 秒" << endl;
        
        if (stats.total_images > 0) {
            cout << "  成功率: " << fixed << setprecision(1) 
                 << (100.0 * stats.processed_images / stats.total_images) << "%" << endl;
            cout << "  平均处理时间: " << fixed << setprecision(3)
                 << (stats.total_time / stats.total_images) << " 秒/图像" << endl;
        }
        
        cout << "\n输出文件:" << endl;
        cout << "  对齐人脸: " << output_dir << "/aligned/" << endl;
        cout << "  标注图像: " << output_dir << "/annotated/" << endl;
        cout << "  处理报告: " << output_dir << "/processing_report.txt" << endl;
    }
};

void showHelp(const string& program_name) {
    cout << "人脸对齐数据生成器" << endl;
    cout << "基于BSTFaceUnlock生成对齐后的人脸数据集" << endl;
    cout << endl;
    cout << "用法: " << program_name << " [选项]" << endl;
    cout << endl;
    cout << "选项:" << endl;
    cout << "  -i, --input <dir>       输入图像目录 (必需)" << endl;
    cout << "  -o, --output <dir>      输出目录 (默认: align_output)" << endl;
    cout << "  -p, --predictor <file>  dlib关键点预测器路径" << endl;
    cout << "  -h, --help              显示此帮助信息" << endl;
    cout << endl;
    cout << "示例:" << endl;
    cout << "  " << program_name << " -i input_images/ -o output_data/" << endl;
    cout << "  " << program_name << " -i faces/ -o aligned_faces/ -p shape_predictor_68_face_landmarks.dat" << endl;
    cout << endl;
    cout << "输出结构:" << endl;
    cout << "  output_dir/" << endl;
    cout << "  ├── aligned/           # 对齐后的人脸图像" << endl;
    cout << "  ├── annotated/         # 带标注的原图" << endl;
    cout << "  └── processing_report.txt  # 处理报告" << endl;
}

int main(int argc, char* argv[]) {
    string input_dir;
    string output_dir = "align_output";
    string predictor_path;
    
    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            showHelp(argv[0]);
            return 0;
        } else if (arg == "-i" || arg == "--input") {
            if (i + 1 < argc) {
                input_dir = argv[++i];
            } else {
                cout << "错误: " << arg << " 需要参数" << endl;
                return 1;
            }
        } else if (arg == "-o" || arg == "--output") {
            if (i + 1 < argc) {
                output_dir = argv[++i];
            } else {
                cout << "错误: " << arg << " 需要参数" << endl;
                return 1;
            }
        } else if (arg == "-p" || arg == "--predictor") {
            if (i + 1 < argc) {
                predictor_path = argv[++i];
            } else {
                cout << "错误: " << arg << " 需要参数" << endl;
                return 1;
            }
        } else {
            cout << "错误: 未知选项 " << arg << endl;
            showHelp(argv[0]);
            return 1;
        }
    }
    
    // 检查必需参数
    if (input_dir.empty()) {
        cout << "错误: 必须指定输入目录" << endl;
        showHelp(argv[0]);
        return 1;
    }
    
    try {
        cout << "=== 人脸对齐数据生成器 ===" << endl;
        
        // 创建数据生成器
        AlignDataMaker maker(input_dir, output_dir, predictor_path);
        
        // 初始化
        if (!maker.initialize()) {
            cout << "错误: 初始化失败" << endl;
            return 1;
        }
        
        // 生成数据集
        maker.generateDataset();
        
        return 0;
        
    } catch (const exception& e) {
        cout << "错误: " << e.what() << endl;
        return 1;
    } catch (...) {
        cout << "错误: 未知异常" << endl;
        return 1;
    }
}