int InnerAlgorithm(const BSTFaceUnlockFrameData* const bst_face_input,const int iso, BSTFaceFeature* const bst_face_output, FilterConfig filter_config, int inner_type) {
    TIMER_START(InnerAlgoTotal)
    TIMER_START(PreProcess)
    if (inner_type == 1) {
        LOGI("InnerEnroll");
    } else if (inner_type == 2) {
        LOGI("InnerAuth");
    }
    int            ret = BST_FU_E_INNER_ERROR;
    unsigned char* input_rotate = NULL;
    unsigned char* input_rgb = NULL;
    unsigned char* tmp_src_img = NULL;
    unsigned char* top_src_img = NULL;
    unsigned char* input_gray = NULL;

    int            rotate_width;
    int            rotate_height;
    int            top_order = 0;
    ncnn::Mat      align_face(NORM_W, NORM_H, NORM_C, 1);
    memset(&g_dump_info, 0, sizeof(DumpInfo));
    g_dump_info.iso = iso;
    do {
/***********************************************************************************************************************************************************************
 *  1.       . frame data transformation
 */
        int input_yuv_size;
        if (bst_face_input->format == BST_IMAGE_TYPE_NV21 || bst_face_input->format == BST_IMAGE_TYPE_NV12) {
            input_yuv_size = bst_face_input->width * bst_face_input->height * 1.5;
        } else {
            ret = BST_FU_E_INVALID_INPUT;
            break;
        }
        input_rotate = new unsigned char[input_yuv_size];
        if (NULL == input_rotate) {
            ret = BST_FU_E_OOM;
            break;
        }

        if (bst_face_input->rotation == 0) {
            memcpy(input_rotate, bst_face_input->data, input_yuv_size);
            rotate_width  = bst_face_input->width;
            rotate_height = bst_face_input->height;
        } else if (bst_face_input->rotation == 270) {
            bstutils::rotate_yuv_nv21_270((unsigned char*)bst_face_input->data, input_rotate, bst_face_input->width, bst_face_input->height, bst_face_input->stride);
            rotate_width  = bst_face_input->height;
            rotate_height = bst_face_input->width;
        } else {
            ret = BST_FU_E_INVALID_INPUT;
            break;
        }
        input_rgb           = new unsigned char[rotate_width * rotate_height * CHANNEL];
        if (NULL == input_rgb) {
            ret = BST_FU_E_OOM;
            break;
        }
        if (bst_face_input->format == BST_IMAGE_TYPE_NV21) {
//            ncnn::yuv420sp2rgb(input_rotate, rotate_width, rotate_height, input_rgb);
             bstutils::nv21_to_rgb_keepsize(input_rotate, rotate_width, rotate_height, rotate_width, rotate_width, input_rgb);
        } else if (bst_face_input->format == BST_IMAGE_TYPE_NV12) {
            ncnn::yuv420sp2rgb_nv12(input_rotate, rotate_width, rotate_height, input_rgb);
            // bstutils::nv12_to_rgb_keepsize(input_rotate, rotate_width, rotate_height, rotate_width, rotate_width, input_rgb);
        } else {
            ret = BST_FU_E_INVALID_INPUT;
            break;
        }
        TIMER_STOP(PreProcess)

#ifdef DUMP
#define DUMP
        g_blink->g_count++;
        bstutils::save_image_png("input_rgb"+std::to_string(g_blink->g_count), input_rgb, rotate_width, rotate_height, 3);
#endif
/***********************************************************************************************************************************************************************
 *   2. 人脸 detect
 */
        TIMER_START(Detect)
        std::vector<BBox> rotate_objects;
        float               max_area                      = 0;
        float               max_idx                       = -1;
//        if (inner_type == 1) {
//            g_detect->perfom(input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects);
//        }
//        else if (inner_type == 2)

        {
            int rotate_order[4] = {0, 1, 3, 2};
            /// 4 次人脸检测
            {
                if (top_src_img == NULL){
                    top_src_img = new unsigned char[rotate_width * rotate_height * CHANNEL];
                    if (NULL == top_src_img) {
                        ret = BST_FU_E_OOM;
                        break;
                    }
                }


                for (int i = 0; i < 4; ++i) {


                    int rotate_type = rotate_order[i];

                    int tmp_width  = (rotate_type == 2 || rotate_type == 0) ? rotate_width : rotate_height;
                    int tmp_height = (rotate_type == 2 || rotate_type == 0) ? rotate_height : rotate_width;
                    if (tmp_src_img == NULL) {
                        tmp_src_img = new unsigned char[tmp_width * tmp_height * CHANNEL];
                        if (NULL == tmp_src_img) {
                            ret = BST_FU_E_OOM;
                            break;
                        }
                    }

                    bstutils::rotate_c3((uint8_t*)input_rgb, tmp_src_img, rotate_width, rotate_height, rotate_type);
                    std::vector<BBox> tmp_objects;
                    if (inner_type == 1){
                        g_detect->perfom(tmp_src_img, tmp_width, tmp_height, CHANNEL, tmp_objects, true, filter_config.min_face);
                    } else{
                        g_detect->perfom(tmp_src_img, tmp_width, tmp_height, CHANNEL, tmp_objects, false, filter_config.min_face);
                    }

                    if (tmp_objects.size() > 0){
                        LOGI("rotate_type:%d, face_num:%d", rotate_type, tmp_objects.size())
                        std::vector<BBox> swap_objects = tmp_objects;
                        tmp_objects = rotate_objects;
                        rotate_objects = swap_objects;
                        if ( i != 0){
                            memcpy(input_rgb, tmp_src_img, sizeof(unsigned char) * rotate_width * rotate_height * CHANNEL);
                        }

                        rotate_width  = tmp_width;
                        rotate_height = tmp_height;
                        top_order         = rotate_type;

                        break;
                    }

                }

            }
        }
#ifdef RECORDING_MODE
        if (inner_type == 2){
            LOGI("RECORDING_MODE");
            ret = BST_FU_CONTINUE; /// BST_FU_CONTINUE 30fps  BST_FU_C_FACE_NOT_FOUND 20fps  BST_FU_SUCCESS  1fps
            break;
        }
       
#endif
        g_dump_info.rotate_order = top_order;
        if (inner_type == 1){ /// 注册
            if (rotate_objects.size() <= 0) {
                g_continue_multi_person_cnt = 0;
                ret = BST_FU_C_FACE_NOT_FOUND;
                break;
            } else if (rotate_objects.size() > 1 ) {
                int board_face_num = 0;
                for (int i = 0; i < (int)rotate_objects.size(); ++i) {
                    if (!outside_border(rotate_objects[i], rotate_width, rotate_height, filter_config)) {
                        board_face_num++;
                    }
                }
                //        LOGE("Enroll,board_face_num: %d", (int)board_face_num);
                if (board_face_num > 1) {
                    g_continue_multi_person_cnt ++;
                }else{
                    g_continue_multi_person_cnt = 0;
                }
            } else if (rotate_objects.size() == 1) {
                for (int i = 0; i < (int)rotate_objects.size(); ++i) {
                    // 选取面积最大人脸框
                    if (rotate_objects[i].w * rotate_objects[i].h > max_area) {
                        max_area = rotate_objects[i].w * rotate_objects[i].h;
                        max_idx  = i;
                    }
                }
                g_continue_multi_person_cnt = 0;
            }
            if (g_continue_multi_person_cnt >= filter_config.max_continue_real){
                LOGE("Enroll, Continue Multi Person, Frame Num: %d", (int)g_continue_multi_person_cnt);
                ret = BST_FU_F_ENROLL_MPERSON;
                g_continue_real_cnt = 0;
                break;
            }else if (g_continue_multi_person_cnt > 0){
                ret = BST_FU_CONTINUE;
                break;
            }else{
                int ret_judge = 0;
                if (!judge_border(rotate_objects[max_idx], rotate_width, rotate_height, filter_config, ret_judge)) {
                    ret = ret_judge;
                    g_continue_real_cnt = 0;
                    break;
                }
            }

        }else if (inner_type == 2){  ///解锁
            if (rotate_objects.size() <= 0) {
                ret = BST_FU_C_FACE_NOT_FOUND;
                break;
            }
            for (int i = 0; i < (int)rotate_objects.size(); ++i) {
                // 选取面积最大人脸框
                if (rotate_objects[i].w * rotate_objects[i].h > max_area) {
                    max_area = rotate_objects[i].w * rotate_objects[i].h;
                    max_idx  = i;
                }
            }
        }

//#define DUMP
#ifdef DUMP
        bstutils::DrawRect((uint8_t*)input_rgb, rotate_width, rotate_height, 3, rotate_objects[0].x, rotate_objects[0].y, rotate_objects[0].x + rotate_objects[0].w, rotate_objects[0].y + rotate_objects[0].h, 1, 0, 255, 0);

        bstutils::save_image_png("rectImage"+std::to_string(g_blink->g_count), input_rgb, rotate_width, rotate_height, 3);
#endif
        g_dump_info.facebox_x1 = rotate_objects[max_idx].x;
        g_dump_info.facebox_y1 = rotate_objects[max_idx].y;
        g_dump_info.facebox_x2 = rotate_objects[max_idx].x + rotate_objects[max_idx].w;
        g_dump_info.facebox_y2 = rotate_objects[max_idx].y + rotate_objects[max_idx].h;
/***********************************************************************************************************************************************************************
 *   2.1 靠近边缘提示
 */
        //        if (!g_detect->judge_border(rotate_objects[max_idx], rotate_width, rotate_height, filter_config)) {
        //            ret = BST_FU_C_NEAR_BORDER;
        //            break;
        //        }

        //        LOGI("object max_idx : %d, %d, %d, %d, %d", max_idx, rotate_objects[max_idx].x, rotate_objects[max_idx].y, rotate_objects[max_idx].w, rotate_objects[max_idx].h);
        TIMER_STOP(Detect)
        Bbox box;
        box.x1 = rotate_objects[max_idx].x;
        box.y1 = rotate_objects[max_idx].y;
        box.x2 = rotate_objects[max_idx].w + rotate_objects[max_idx].x;
        box.y2 = rotate_objects[max_idx].h + rotate_objects[max_idx].y;




/***********************************************************************************************************************************************************************
 *  2.3       motion detect，人脸运动检测 plus for motion detection
 */
        if ( inner_type ==2 && filter_config.live_filter_enable){

            if (g_auth_frame_num % 1 == 0
                //                && filter_config.max_continue_real > 1
            ){

                TIMER_START(LivingM);
                if(input_gray == NULL){
                    input_gray = new unsigned char [rotate_width*rotate_height];
                    if (NULL == input_gray) {
                        ret = BST_FU_E_OOM;
                        break;
                    }
                }
                bstutils::RGB2Gray((uint8_t*)input_rgb, rotate_width, rotate_height, input_gray);

                if (g_first_in == 0){
                    // 3 plus for motion detection
                    move_param_t m_param = {0};
                    m_param.height_divide_count = 2;
                    m_param.width_divide_count = 2;
                    std::vector<int> thresholds;
                    thresholds.clear();
                    for (int i = 0; i < m_param.height_divide_count * m_param.width_divide_count; ++i) {
                        thresholds.push_back(filter_config.motion_abs_thres);
                    }
                    m_param.thresholds = thresholds.data();
                    m_param.roi_detects_count = m_param.height_divide_count * m_param.width_divide_count;
                    m_param.motion_rel_thres = filter_config.motion_rel_thres;
                    int ret_value = g_motion_detector->motion_cache(m_param,input_gray, rotate_width,rotate_height,box);
                    g_dump_info.livingM_flag = -1;
                    if(ret_value!= 0){
                        LOGE("LivingM_cache failed");
                        g_continue_real_cnt = 0;
                        memset(&g_last_cache_bbox, 0, sizeof(Bbox));
                        g_first_in = 0;
                        ret = BST_FU_E_INNER_ERROR;
                        TIMER_STOP(LivingM);
                        break;
                    }
                    memcpy(&g_last_cache_bbox, &box, sizeof(Bbox));
                    g_first_in ++;

                    g_continue_real_cnt = 1;
                    ret = BST_FU_CONTINUE;
                    TIMER_STOP(LivingM);
                    break;

                }
                else{

                    int livingM_flag = -1;
                    int ret_value = g_motion_detector->motion_detect(input_gray, rotate_width,rotate_height,g_last_cache_bbox,livingM_flag, g_first_in);
                    if(ret_value!= 0){
                        LOGE("LivingM_detect failed");
                        g_continue_real_cnt = 0;
                        g_first_in = 0;
                        memset(&g_last_cache_bbox, 0, sizeof(Bbox));
                        TIMER_STOP(LivingM);
                    }else
                    {
                        g_first_in ++;
                        g_dump_info.livingM_flag = livingM_flag;

                        memcpy(&g_last_cache_bbox, &box, sizeof(Bbox));

                        move_param_t m_param = {0};

                        m_param.height_divide_count = filter_config.motion_height_divide_count;
                        m_param.width_divide_count = filter_config.motion_width_divide_count;
                        std::vector<int> thresholds;
                        thresholds.clear();
                        for (int i = 0; i < m_param.height_divide_count * m_param.width_divide_count; ++i) {
                            thresholds.push_back(filter_config.motion_abs_thres);
                        }
                        m_param.thresholds = thresholds.data();
                        m_param.roi_detects_count = m_param.height_divide_count * m_param.width_divide_count;
                        m_param.motion_rel_thres = filter_config.motion_rel_thres;
                        ret_value = g_motion_detector->motion_cache(m_param,input_gray, rotate_width,rotate_height,box);
                        if(ret_value!= 0){
                            LOGE("LivingM_cache failed");
                            g_continue_real_cnt = 0;
                            g_first_in = 0;
                            memset(&g_last_cache_bbox, 0, sizeof(Bbox));
                            ret = BST_FU_E_INNER_ERROR;
                            TIMER_STOP(LivingM);
                            break;
                        }
                        if (livingM_flag == 0){
                            g_continue_real_cnt = 0;
                            ret = BST_FU_C_SUSPECTED;
                            TIMER_STOP(LivingM);
                            break;
                        }
                    }
                }
                TIMER_STOP(LivingM);
            }

        }


/****************************************************************************************************************************************************************
* 2.2 屏幕与打印纸检测
*/
        if(filter_config.live_filter_enable){
            TIMER_START(LivingD);
#ifdef SECUREOS
            g_screen_paper_detect = new ModuleScreenPaperDetect();
            g_screen_paper_detect->init();
#endif
            g_dump_info.liveD_score = -1.f;
            std::vector<Object> screen_paper_objects;
            if (inner_type == 1){
                g_screen_paper_detect->perfom(input_rgb, rotate_width, rotate_height, CHANNEL, screen_paper_objects,box, true, filter_config.min_face);
            } else{
                g_screen_paper_detect->perfom(input_rgb, rotate_width, rotate_height, CHANNEL, screen_paper_objects,box, false, filter_config.min_face);
            }
            if(screen_paper_objects.size() > 0){
                if(screen_paper_objects[0].class_id == 0){
                    g_dump_info.liveD_score = screen_paper_objects[0].score;
                }else if (screen_paper_objects[0].class_id == 1){
                    g_dump_info.liveD_score = 1 + screen_paper_objects[0].score;
                }
                g_continue_real_cnt = 0;
                g_continue_livingD_cnt +=1;
                if (g_continue_livingD_cnt >= filter_config.max_livingD_suspected_live_attack){
                   
                    if ( filter_config.after_suspected_continue_real > filter_config.max_continue_real){
                        g_processing_max_continue_real = filter_config.after_suspected_continue_real;
                    }else{
                        g_processing_max_continue_real = filter_config.max_continue_real;
                    }

                }

                /// cache features

                ret = BST_FU_C_SUSPECTED;
//                if (inner_type == 1){
//                    TIMER_STOP(LivingD);
//                    break;
//                }

            }else{
//                g_continue_livingD_cnt = 0;
            }
#ifdef SECUREOS
            g_screen_paper_detect->release();
            delete g_screen_paper_detect;
            g_screen_paper_detect = NULL;
#endif
            TIMER_STOP(LivingD);
        }

/***********************************************************************************************************************************************************************
 *   3. 人脸 关键点 + blur + mask  属性
 */
        TIMER_START(Landmark)
#ifdef SECUREOS
        g_landmarker = new ModuleLandmarker();
        g_landmarker->init();
#endif
        LmkInfo lmk_info;
        bool land_ret =g_landmarker->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], lmk_info);
        if (land_ret == false){
            ret = BST_FU_E_INNER_ERROR;
            TIMER_STOP(Landmark)
            break;
        }
#ifdef SECUREOS
        g_landmarker->release();
        delete g_landmarker;
        g_landmarker = NULL;
#endif
        /***********************************************************************************************************************************************************************
 *   3.1 angel，角度过滤
 */
        g_dump_info.yaw = lmk_info.poses[0];
        g_dump_info.pitch = lmk_info.poses[1];
        g_dump_info.roll = lmk_info.poses[2];
        if (filter_config.angel_filter_enable) {
            float yaw = lmk_info.poses[0];  // yaw,pitch,roll
            float pitch = lmk_info.poses[1];  // yaw,pitch,roll
            /// yaw > 负值 left_pitch_thres抬头  正值  right_yaw_thres 低头角度
           
            if (yaw < -filter_config.left_yaw_thres || yaw > filter_config.right_yaw_thres ) {
                ret = BST_FU_C_POSE_ABNORMAL;
                g_continue_real_cnt = 0;
                TIMER_STOP(Landmark)
                break;
            }
            /// pitch 负值 up_pitch_thres抬头  正值  down_pitch_thres 低头角度
            if (pitch < -filter_config.up_pitch_thres || pitch > filter_config.down_pitch_thres) {
                ret = BST_FU_C_POSE_ABNORMAL;
                g_continue_real_cnt = 0;
                TIMER_STOP(Landmark)
                break;
            }
        }



        for (int i = 0; i < LMK_NUM; i++) {
            box.ppoint[i * 2 + 0] = lmk_info.pts[i].x;
            box.ppoint[i * 2 + 1] = lmk_info.pts[i].y;
        }
//#define DUMP
#ifdef DUMP
        bstutils::DrawRect((uint8_t*)input_rgb, rotate_width, rotate_height, 3, rotate_objects[0].x, rotate_objects[0].y, rotate_objects[0].x + rotate_objects[0].w, rotate_objects[0].y + rotate_objects[0].h, 1, 0, 255, 0);

        for (int i = 0; i < lmk_info.pts.size(); ++i) {
            // if (lmk_info.vis_probs[i] > 0) {
            bstutils::DrawPoint((uint8_t*)input_rgb, rotate_width, rotate_height, 3, lmk_info.pts[i].x, lmk_info.pts[i].y, 2, 255, 0, 0);
            //     //            LOGI("DrawPoint:%f , %f",lmk_info.pts[i].x,lmk_info.pts[i].y);
            // }
        }
        bstutils::save_image_png("landmarkImage"+std::to_string(g_blink->g_count), input_rgb, rotate_width, rotate_height, 3);
#endif

        g_dump_info.mask_score =  lmk_info.mask_prob;
        TIMER_STOP(Landmark)






/***********************************************************************************************************************************************************************
 *  4.       bright，亮度估计
 */
        TIMER_START(Quality)
        QualityOutput quality_output;
        if (inner_type == 1){
            if (iso >0){
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], false,true,quality_output);
            }else
            {
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], true,true,quality_output);
            }
            g_dump_info.quality_score = g_quality_assessor->getQualityScore(quality_output,lmk_info);
        }else if (inner_type == 2){
            if (iso >0){
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], false, false,quality_output);
            }else
            {
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], true,false,quality_output);
            }
        }


        // 6.bright，亮度估计
        if (filter_config.bright_filter_enable) {

            g_dump_info.face_brightness = quality_output.face_brightness;
            g_dump_info.bg_brightness = quality_output.bg_brightness;
            // if (1 == inner_type) {
            //     if (iso >= filter_config.iso_dark_thres) {
            //         g_continue_real_cnt=0;
            //         LOGI("enroll iso check : %d", iso);
            //         ret = BST_FU_C_TOO_DARK;
            //         break;
            //     }
            // }
            if(inner_type == 1){
                if (quality_output.face_brightness <= filter_config.face_dark_level_1_thres) {
                    g_continue_real_cnt=0;
                    // LOGI("too dark");
                    ret = BST_FU_C_TOO_DARK;
                    break;
                }
            }else if(inner_type == 2){
                if(iso < filter_config.iso_dark_level_1_thres){ // 更亮的时候，人脸逐步变亮，过滤之前更暗的人脸
                    if (quality_output.face_brightness <= filter_config.face_dark_level_2_thres) {
                        g_continue_real_cnt=0;
                        // LOGI("too dark");
                        ret = BST_FU_C_TOO_DARK;
                        break;
                    }
                }else{
                    if (quality_output.face_brightness <= filter_config.face_dark_level_1_thres) {
                        g_continue_real_cnt=0;
                        // LOGI("too dark");
                        ret = BST_FU_C_TOO_DARK;
                        break;
                    }
                }
            }
            if (quality_output.face_brightness >= filter_config.bright_thres) {
                g_continue_real_cnt=0;
                // LOGI("too bright");
                ret = BST_FU_C_TOO_BRIGHT;
                break;
            }
        }

/***********************************************************************************************************************************************************************
 *  5.       模糊估计
 */


        // 7.blur，模糊估计
        if (filter_config.blur_filter_enable) {
            // 量产
            g_dump_info.model_blur_score = lmk_info.blur_prob;
            g_dump_info.quality_blur_score = quality_output.blurriness;
            if (quality_output.blurriness > filter_config.quality_blur_thres) {
                    g_continue_real_cnt = 0;
                    ret = BST_FU_C_TOO_BLUR;
                    break;
                }
                // }
            if(iso <= 4800){
                if (lmk_info.blur_prob > filter_config.model_blur_thres) {
                    //            LOGI("too blur,model_blur_thres:%f blur_prob:%f",g_config.authen_config.model_blur_thres,lmk_info.blur_prob);
                    g_continue_real_cnt = 0;
                    ret= BST_FU_C_TOO_BLUR;
                    break;
                }
             }
        }
        TIMER_STOP(Quality)


        /***********************************************************************************************************************************************************************
 *  6.       face align，人脸对齐
 */
        TIMER_START(Align)
        std::vector<float> lmks;
        for (int j = 0; j < LMK_NUM; ++j) {
            lmks.push_back(box.ppoint[2 * j]);
            lmks.push_back(box.ppoint[2 * j + 1]);
        }
        uint8_t* p_src   = (uint8_t*)input_rgb;
        uint8_t* p_align = (uint8_t*)align_face.data;
        memset(align_face,0,NORM_W * NORM_H * NORM_C);
        if (!faceAlign((unsigned char*)input_rgb,rotate_width, rotate_height,CHANNEL,(unsigned char*)p_align,NORM_W, NORM_H,box)) {
            LOGE("failed to get norm face!");
            // TODO: wrong return value
            ret = BST_FU_E_INNER_ERROR;
            TIMER_STOP(Align)
            break;
        }
       
        if(quality_output.face_brightness < (filter_config.live_face_dark_thres))
        {
            bstutils::ColorHistogramEqualization(p_align,NORM_W, NORM_H,true,0.5);
            //  bstutils::ColorHistogramEqualizationOriginal(p_align,NORM_W, NORM_H);
        }

#ifdef DUMP
#define DUMP
        bstutils::save_image_png("faceAlign"+std::to_string(g_blink->g_count), p_align, NORM_W, NORM_H, 3);
#endif
        TIMER_STOP(Align)



/***********************************************************************************************************************************************************************
 *  7.       blink judge，睁闭眼判断
 */

    if (filter_config.blink_filter_enable) {
        TIMER_START(Blink)
#ifdef SECUREOS
    g_blink = new ModuleBlink();
    g_blink->init();
#endif
            /// 角度过滤 // yaw,pitch,roll
            // TODO 参数外放
//            if (abs(lmk_info.poses[0]) > 40 || abs(lmk_info.poses[1]) > 40) {
//                ret = BST_FU_C_POSE_ABNORMAL;
//                break;
//            }
/*** landmark vis may not work
            int occ_flag = g_landmarker->judge_occlusion(lmk_info, 0); // 1 遮挡，0 未遮挡
            if (occ_flag == 1) {
                g_dump_info.blink_score = -1.; /// 关键点支持遮挡检测
                ret = BST_FU_C_CLOSED_EYE;
                break;
            }

*/
            int blink_target = 224;
            ncnn::Mat      blinkface(blink_target, blink_target, NORM_C, 1);
            if (!faceAlign((unsigned char*)input_rgb,rotate_width, rotate_height,CHANNEL,(unsigned char*)blinkface,blink_target, blink_target,box)) {
                LOGE("failed to get norm face!");
                // TODO: wrong return value
                ret = BST_FU_E_INNER_ERROR;
                TIMER_STOP(Blink)
                break;
            }                                                                                                                                             // [lx1, ly1, lx2, ly2, lscore, lstatus, rx1, ry1, rx2, ry2, rscore, rstatus]
            bool run_ok         = g_blink->perfom((uint8_t*)blinkface, blink_target, blink_target, CHANNEL, rotate_objects[max_idx], filter_config.blink_thres,filter_config.mask_hole_thres,quality_output.face_brightness,lmk_info); // open score
//            bool run_ok         = g_blink->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], filter_config.blink_thres,quality_output.face_brightness,lmk_info); // open score
#ifdef SECUREOS
    g_blink->release();
    delete g_blink;
    g_blink = NULL;
#endif

            if (run_ok) {
                g_dump_info.blink_score = lmk_info.eye_open_status;
                if (lmk_info.eye_open_status < 1) {
                    ret = BST_FU_C_CLOSED_EYE;
//                    g_continue_livingD_cnt = 0;
//                    g_continue_livingC_cnt = 0;
//                    g_continue_real_cnt = 0;
                    TIMER_STOP(Blink)
                    break;
                }
                else if (lmk_info.eye_open_status > 1 && filter_config.live_dark_thres >0 && filter_config.live_normal_thres > 0  && filter_config.live_filter_enable) {
                    ret = BST_FU_C_SUSPECTED;
//                    g_continue_livingD_cnt = 0;
//                    g_continue_livingC_cnt = 0;
//                    g_continue_real_cnt = 0;
                    TIMER_STOP(Blink)
                    break;
                }

            }
            else {
                ret = BST_FU_E_INNER_ERROR;
                g_continue_real_cnt = 0;
//                g_continue_livingD_cnt = 0;
//                g_continue_livingC_cnt = 0;
                TIMER_STOP(Blink)
                break;
            }
              TIMER_STOP(Blink)
        }

/***********************************************************************************************************************************************************************
 *  8.       遮挡判断
 */

        if (filter_config.occ_filter_enable) {
            if (inner_type == 1) {
                int occ_flag = judge_occlusion(lmk_info,1); // 1 遮挡，0 未遮挡
                if (occ_flag == 1) {
                    ret = BST_FU_C_OCCLUDED;
                    g_continue_real_cnt = 0;
    //                g_continue_livingD_cnt = 0;
    //                g_continue_livingC_cnt = 0;
                    break;
                }
                TIMER_START(Nose)
                #ifdef SECUREOS
                    g_nose = new ModuleNose();
                    g_nose->init();
                #endif                                                                                                                                         // [lx1, ly1, lx2, ly2, lscore, lstatus, rx1, ry1, rx2, ry2, rscore, rstatus]
                bool run_ok         = g_nose->perfom((uint8_t*)align_face, NORM_W, NORM_H, CHANNEL, rotate_objects[max_idx], 0.5,quality_output.face_brightness,lmk_info); // open score
                #ifdef SECUREOS
                    g_nose->release();
                    delete g_nose;
                    g_nose = NULL;
                #endif
                if (run_ok) {
                    g_dump_info.nose_status = lmk_info.nose_status;
                    if (lmk_info.nose_status == 0) {
                        ret = BST_FU_C_OCCLUDED;
                        g_continue_real_cnt = 0;
                        TIMER_STOP(Nose)
                        break;
                    }
                }
                else {
                    ret = BST_FU_E_INNER_ERROR;
                    g_continue_real_cnt = 0;
                    //                g_continue_livingD_cnt = 0;
                    //                g_continue_livingC_cnt = 0;
                    TIMER_STOP(Nose)
                    break;
                }
                TIMER_STOP(Nose)
            }


            /// 口罩
            if (inner_type == 1 ) { /// || inner_type == 2
                if (lmk_info.mask_prob > filter_config.mask_thres) { // 口罩遮挡
                    // LOGI("mask");
                    ret = BST_FU_C_OCCLUDED;
                    g_continue_real_cnt = 0;
//                    g_continue_livingD_cnt = 0;
//                    g_continue_livingC_cnt = 0;
                  
                    break;
                }
            }
        }


/***********************************************************************************************************************************************************************
 *  9.       cal live score，活体检测
 */
        TIMER_START(LivingC)
        if (filter_config.live_filter_enable and ret != BST_FU_C_SUSPECTED) {
            float liveC_score = -1;
            if (
                // iso <=0 ||  
                (quality_output.bg_brightness <= filter_config.live_bg_dark_thres && quality_output.bg_brightness > 0)
                || (iso > filter_config.live_ISO_thres || quality_output.face_brightness <= filter_config.live_face_dark_thres)
                ) {
                LOGI("dark mode");
                float liveC_threshold = filter_config.live_dark_thres;

                {
#ifdef SECUREOS
                g_dark_live = new ModuleLiving();
                if (NULL == g_dark_live) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_dark_live->init(dark_mode);
#endif

                    g_dark_live->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], liveC_score,lmk_info,liveC_threshold);
#ifdef SECUREOS
                    g_dark_live->release();
                    delete g_dark_live;
                    g_dark_live = NULL;
#endif
                }


                g_dump_info.liveC_score = liveC_score;
                if (liveC_score < liveC_threshold) {
                    g_continue_real_cnt = 0;
                    ret = BST_FU_C_SUSPECTED;
                    g_continue_livingC_cnt +=1;
                    if (g_continue_livingC_cnt >= filter_config.max_livingC_suspected_live_attack){

                        if ( filter_config.after_suspected_continue_real > filter_config.max_continue_real){
                            g_processing_max_continue_real = filter_config.after_suspected_continue_real;
                        }else{
                            g_processing_max_continue_real = filter_config.max_continue_real;
                        }

                    }
                }
                else{
                    g_continue_real_cnt++;
                }
            } else{
                LOGI("normal mode");
                float liveC_threshold = filter_config.live_normal_thres;
//                if (abs(lmk_info.poses[0]) > filter_config.match_thres_pitch_thres || abs(lmk_info.poses[1]) > filter_config.match_thres_pitch_thres) { /// pitch大角度
//                    liveC_threshold = filter_config.live_normal_thres;
//                    LOGI("big_angel_mode");
//#ifdef SECUREOS
//                    g_big_angel_live = new ModuleLiving();
//                    g_big_angel_live->init(big_angel_mode);
//#endif
//
//                    g_big_angel_live->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], liveC_score,lmk_info,liveC_threshold);
//#ifdef SECUREOS
//                    g_big_angel_live->release();
//                    delete g_big_angel_live;
//                    g_big_angel_live = NULL;
//#endif
//                }
//                else
                {
#ifdef SECUREOS
                g_normal_live = new ModuleLiving();
                if (NULL == g_normal_live) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_normal_live->init(normal_mode);
#endif

                    g_normal_live->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], liveC_score,lmk_info,liveC_threshold);
#ifdef SECUREOS
                    g_normal_live->release();
                    delete g_normal_live;
                    g_normal_live = NULL;
#endif
                }


                g_dump_info.liveC_score = liveC_score;
                if (liveC_score < liveC_threshold) {
                    g_continue_real_cnt = 0;
                    ret = BST_FU_C_SUSPECTED;
                    g_continue_livingC_cnt +=1;
                    if (g_continue_livingC_cnt >= filter_config.max_livingC_suspected_live_attack){

                        if ( filter_config.after_suspected_continue_real > filter_config.max_continue_real){
                            g_processing_max_continue_real = filter_config.after_suspected_continue_real;
                        }else{
                            g_processing_max_continue_real = filter_config.max_continue_real;
                        }

                    }
                }
                else{
                    g_continue_real_cnt++;
                }
            }

        } 
        else {
            g_continue_real_cnt++;
        }
        TIMER_STOP(LivingC)
/**********************************************************************************************************************************
 * 7. 活体攻击策略，判为活体攻击后，如果判断是同一个人，则一直返回活体攻击
 */
        TIMER_START(LivingStrategy)
        std::vector<float> sus_feature;
       {

            if ( g_suspected_attack_cnt ==0){ ///BST_FU_C_SUSPECTED == ret &&
                // get face feature，人脸特征 arcface by dong.wang

#ifdef SECUREOS
                g_recognizer = new ModuleRecognizer();
                if (NULL == g_recognizer) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_recognizer->init();
#endif

                g_recognizer->perm((uint8_t*)align_face.data, NORM_W, NORM_H, NORM_C, sus_feature);

#ifdef SECUREOS
                g_recognizer->release();
                delete g_recognizer;
                g_recognizer = NULL;
#endif
                memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
                memcpy(g_last_feature_database, sus_feature.data(), sizeof(float) * sus_feature.size());
            }
            else { /// (g_suspected_attack_cnt != 0)

#ifdef SECUREOS
                g_recognizer = new ModuleRecognizer();
                if (NULL == g_recognizer) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_recognizer->init();
#endif

                g_recognizer->perm((uint8_t*)align_face.data, NORM_W, NORM_H, NORM_C, sus_feature);

#ifdef SECUREOS
                g_recognizer->release();
                delete g_recognizer;
                g_recognizer = NULL;
#endif
                LOGI("g_continue_livingD_cnt:%d g_continue_livingC_cnt:%d"
                     "",g_continue_livingD_cnt,g_continue_livingC_cnt)
                if (g_continue_livingD_cnt >= filter_config.max_livingD_suspected_live_attack ||
                    g_continue_livingC_cnt  >= filter_config.max_livingC_suspected_live_attack){
                    float  score = 0;
                    bstutils::bstFeatureCompare(g_last_feature_database,sus_feature.data(),sus_feature.size(),score);
                    if (score > filter_config.match_thres){
                        /// 连续是同一个人
                        LOGI("%.2f same person,keep suspected",score)
                        ret = BST_FU_C_SUSPECTED;

                    }else{
                        LOGI("%.2f,not same person",score)
                        g_suspected_attack_cnt = 0;
                        g_continue_livingD_cnt = 0;
                        g_continue_livingC_cnt = 0;
                        g_suspected_attack_frame_cnt = 0;
                    }
                }
//                LOGI("FET: %.4f, %.4f, %.4f", feature[0], feature[100], feature[FEATURE_DIM_SDK - 1]);

                memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
                memcpy(g_last_feature_database, sus_feature.data(), sizeof(float) * sus_feature.size());
            }
            LOGI("ret:%x",ret);
            if (BST_FU_C_SUSPECTED == ret){
                g_continue_real_cnt = 0;
                TIMER_STOP(LivingStrategy)
                break;
            }
        }
        TIMER_STOP(LivingStrategy)

/***********************************************************************************************************************************************************************
 *  10.        人脸识别
 */
        TIMER_START(Feature)
        LOGI("InnerAlgorithm Feature");
        bst_face_output->threshold = 0.;
        if ( inner_type == 1){
            if (g_enroll_quality > g_dump_info.quality_score){
                g_enroll_quality = g_dump_info.quality_score;
                if(g_enroll_align_face == NULL){
                    g_enroll_align_face = new unsigned char[NORM_W*NORM_H* NORM_C];
                    if (NULL == g_enroll_align_face) {
                        ret = BST_FU_E_OOM;
                        break;
                    }
                }
                memcpy(g_enroll_align_face,align_face.data,NORM_W*NORM_H*NORM_C*sizeof(unsigned char) );
                LOGI("best face quality");
            }
        }

        // 增加活体检测连续次数判断
        LOGI("g_continue_real_cnt: %d , g_processing_max_continue_real: %d", g_continue_real_cnt, g_processing_max_continue_real)

        if (g_continue_real_cnt >= g_processing_max_continue_real) { ///

#ifdef DUMP
            bstutils::save_image_png("align_face"+std::to_string(g_blink->g_count), align_face, NORM_W, NORM_H, CHANNEL);
#endif

            // get face feature，人脸特征 arcface by dong.wang
            if(sus_feature.size() == 0){


                std::vector<float> feature;
    #ifdef SECUREOS
                g_recognizer = new ModuleRecognizer();
                if (NULL == g_recognizer) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_recognizer->init();
    #endif


                if ( inner_type == 1){
                    g_recognizer->perm((uint8_t*)g_enroll_align_face, NORM_W, NORM_H, NORM_C, sus_feature);
                }else{
                    g_recognizer->perm((uint8_t*)align_face.data, NORM_W, NORM_H, NORM_C, sus_feature);
                }
    #ifdef SECUREOS
                g_recognizer->release();
                delete g_recognizer;
                g_recognizer = NULL;
    #endif
            }
            memset(bst_face_output->feature, 0, FEATURE_DIM_SDK * sizeof(float));
            memcpy(bst_face_output->feature, sus_feature.data(), sizeof(float) * sus_feature.size());

            if (inner_type == 1){
#ifdef DEBUG_MATCH
                //            int offset = FEATURE_DIM_SDK / 2;
                //            offset     = offset > feature.size() ? feature.size() : offset;
                //            memcpy(g_feature_database, bst_face_output->feature, offset);
                //            memcpy(g_feature_database + offset, bst_face_output->feature, offset);
                memset(g_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
                memcpy(g_feature_database, sus_feature.data(), sizeof(float) * sus_feature.size());
#endif
            }
            if (inner_type == 1){
                ret = BST_FU_SUCCESS;
            } else {

                if (lmk_info.mask_prob > filter_config.mask_thres) { /// 带口罩
                    bst_face_output->threshold = filter_config.match_thres_withmask;
                }

                else if (lmk_info.poses[0] < -filter_config.match_thres_left_pitch_thres || lmk_info.poses[0] > filter_config.match_thres_right_pitch_thres) {
                    bst_face_output->threshold = filter_config.match_thres_big_pitch;
                }

                //pitch 大角度 // yaw,pitch,roll
                // pitch 负值 up_pitch_thres抬头  正值  down_pitch_thres 低头角度
                else if (lmk_info.poses[1] < -filter_config.match_thres_up_pitch_thres || lmk_info.poses[1] > filter_config.match_thres_down_pitch_thres) {
                    bst_face_output->threshold = filter_config.match_thres_big_pitch;
                }
                else if (quality_output.face_brightness < filter_config.live_face_dark_thres) { /// 大角度
                    bst_face_output->threshold = filter_config.match_thres_big_pitch;
                }
                else {
                    bst_face_output->threshold = filter_config.match_thres;
                }
                // 暗光、逆光 模糊人脸，阈值降低
                if(iso > filter_config.iso_dark_level_1_thres){
                    if(g_dump_info.model_blur_score > 0.5){
                        bst_face_output->threshold = filter_config.match_thres_big_pitch;
                    }
                }

                ret = BST_FU_CONTINUE;
            }
//            g_continue_real_cnt = 0;
            TIMER_STOP(Feature)
            break;
        }
        /// 加入注册选择质量最优
        else {

        }
        TIMER_STOP(Feature)
        ret = BST_FU_CONTINUE;
        break;
    } while (false);

    if (input_rotate) delete[] input_rotate;
    if (input_rgb) delete[] input_rgb;
    if (tmp_src_img)  delete[] tmp_src_img;
    if (top_src_img) delete[] top_src_img;
    if (input_gray) delete[] input_gray;

    TIMER_STOP(InnerAlgoTotal)
    g_dump_info.ret_val = ret;

    return ret;
}
