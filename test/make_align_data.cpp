#include "BSTFaceUnlock.h"
#include <opencv2/opencv.hpp>
#include <dlib/opencv.h>
#include <dlib/image_processing/frontal_face_detector.h>
#include <dlib/image_processing/render_face_detections.h>
#include <dlib/image_processing.h>
#include <dlib/gui_widgets.h>
#include <dlib/image_io.h>
#include <dlib/dnn.h>
#include <iostream>
#include <vector>
#include <memory>

using namespace cv;
using namespace dlib;
using namespace std;

class BSTFaceUnlock::Impl {
public:
    frontal_face_detector detector;
    shape_predictor sp;
    bool predictor_loaded;
    
    Impl() : predictor_loaded(false) {
        detector = get_frontal_face_detector();
    }
    
    bool loadPredictor(const string& predictor_path) {
        try {
            deserialize(predictor_path) >> sp;
            predictor_loaded = true;
            return true;
        } catch (const exception& e) {
            cout << "Error loading predictor: " << e.what() << endl;
            return false;
        }
    }
};

BSTFaceUnlock::BSTFaceUnlock() : pImpl(make_unique<Impl>()) {}

BSTFaceUnlock::~BSTFaceUnlock() = default;

bool BSTFaceUnlock::initialize(const string& predictor_path) {
    return pImpl->loadPredictor(predictor_path);
}

vector<Rect> BSTFaceUnlock::detectFaces(const Mat& image) {
    vector<Rect> faces;
    
    if (image.empty()) {
        return faces;
    }
    
    // Convert to dlib format
    cv_image<bgr_pixel> dlib_img(image);
    
    // Detect faces
    vector<rectangle> dets = pImpl->detector(dlib_img);
    
    // Convert to OpenCV format
    for (const auto& det : dets) {
        faces.emplace_back(det.left(), det.top(), det.width(), det.height());
    }
    
    return faces;
}

vector<Point2f> BSTFaceUnlock::detectLandmarks(const Mat& image, const Rect& face_rect) {
    vector<Point2f> landmarks;
    
    if (!pImpl->predictor_loaded) {
        cout << "Predictor not loaded!" << endl;
        return landmarks;
    }
    
    if (image.empty()) {
        return landmarks;
    }
    
    // Convert to dlib format
    cv_image<bgr_pixel> dlib_img(image);
    rectangle dlib_rect(face_rect.x, face_rect.y, 
                       face_rect.x + face_rect.width, 
                       face_rect.y + face_rect.height);
    
    // Detect landmarks
    full_object_detection shape = pImpl->sp(dlib_img, dlib_rect);
    
    // Convert to OpenCV format
    for (int i = 0; i < shape.num_parts(); ++i) {
        landmarks.emplace_back(shape.part(i).x(), shape.part(i).y());
    }
    
    return landmarks;
}

Mat BSTFaceUnlock::alignFace(const Mat& image, const vector<Point2f>& landmarks) {
    if (landmarks.size() < 5) {
        cout << "Not enough landmarks for alignment!" << endl;
        return image.clone();
    }
    
    // Define standard face template points
    vector<Point2f> template_points = {
        Point2f(30.2946f, 51.6963f),  // Left eye
        Point2f(65.5318f, 51.5014f),  // Right eye
        Point2f(48.0252f, 71.7366f),  // Nose tip
        Point2f(33.5493f, 92.3655f),  // Left mouth corner
        Point2f(62.7299f, 92.2041f)   // Right mouth corner
    };
    
    // Extract key points from landmarks (assuming 68-point model)
    vector<Point2f> face_points;
    if (landmarks.size() >= 68) {
        // Left eye center (average of points 36-39)
        Point2f left_eye = (landmarks[36] + landmarks[37] + landmarks[38] + landmarks[39]) / 4.0f;
        // Right eye center (average of points 42-45)
        Point2f right_eye = (landmarks[42] + landmarks[43] + landmarks[44] + landmarks[45]) / 4.0f;
        // Nose tip (point 30)
        Point2f nose_tip = landmarks[30];
        // Left mouth corner (point 48)
        Point2f left_mouth = landmarks[48];
        // Right mouth corner (point 54)
        Point2f right_mouth = landmarks[54];
        
        face_points = {left_eye, right_eye, nose_tip, left_mouth, right_mouth};
    } else {
        // Use first 5 points if less than 68 landmarks
        face_points.assign(landmarks.begin(), landmarks.begin() + min(5, (int)landmarks.size()));
    }
    
    // Calculate transformation matrix
    Mat transform_matrix = estimateAffinePartial2D(face_points, template_points);
    
    if (transform_matrix.empty()) {
        cout << "Failed to calculate transformation matrix!" << endl;
        return image.clone();
    }
    
    // Apply transformation
    Mat aligned_face;
    warpAffine(image, aligned_face, transform_matrix, Size(96, 112));
    
    return aligned_face;
}

int BSTFaceUnlock::InnerAlgorithm(const BSTFaceUnlockFrameData* const bst_face_input,
                                  const int iso,
                                  BSTFaceFeature* const bst_face_output,
                                  FilterConfig filter_config,
                                  int inner_type) {
    
    if (!bst_face_input || !bst_face_output) {
        return -1; // Invalid input
    }
    
    // Convert input data to OpenCV Mat
    Mat input_image;
    if (bst_face_input->format == 2) { // GRAY
        input_image = Mat(bst_face_input->height, bst_face_input->width, 
                         CV_8UC1, bst_face_input->data);
    } else if (bst_face_input->format == 0) { // RGB
        input_image = Mat(bst_face_input->height, bst_face_input->width, 
                         CV_8UC3, bst_face_input->data);
        cvtColor(input_image, input_image, COLOR_RGB2BGR);
    } else { // BGR
        input_image = Mat(bst_face_input->height, bst_face_input->width, 
                         CV_8UC3, bst_face_input->data);
    }
    
    // Detect faces
    vector<Rect> faces = detectFaces(input_image);
    
    if (faces.empty()) {
        bst_face_output->face_count = 0;
        return 0; // No faces detected
    }
    
    // Process the first detected face
    Rect face_rect = faces[0];
    
    // Check face size constraints
    float face_size = max(face_rect.width, face_rect.height);
    if (face_size < filter_config.min_face_size || face_size > filter_config.max_face_size) {
        bst_face_output->face_count = 0;
        return 0; // Face size out of range
    }
    
    // Store bounding box
    bst_face_output->bbox[0] = face_rect.x;
    bst_face_output->bbox[1] = face_rect.y;
    bst_face_output->bbox[2] = face_rect.width;
    bst_face_output->bbox[3] = face_rect.height;
    
    // Detect landmarks if predictor is loaded
    vector<Point2f> landmarks = detectLandmarks(input_image, face_rect);
    
    // Store landmarks
    if (!landmarks.empty() && landmarks.size() <= 68) {
        for (size_t i = 0; i < landmarks.size() && i < 68; ++i) {
            bst_face_output->landmarks[i][0] = landmarks[i].x;
            bst_face_output->landmarks[i][1] = landmarks[i].y;
        }
    }
    
    // Calculate confidence based on face size and detection quality
    float size_confidence = min(1.0f, face_size / 100.0f); // Normalize to 100x100 standard
    bst_face_output->confidence = size_confidence;
    
    // Check confidence threshold
    if (bst_face_output->confidence < filter_config.confidence_threshold) {
        bst_face_output->face_count = 0;
        return 0; // Confidence too low
    }
    
    // Face alignment if enabled
    if (filter_config.enable_alignment && !landmarks.empty()) {
        Mat aligned_face = alignFace(input_image, landmarks);
        // Note: In a real implementation, you might want to store or return the aligned face
    }
    
    bst_face_output->face_count = 1;
    
    return 1; // Success
}

bool BSTFaceUnlock::processImage(const string& image_path, const string& output_path) {
    Mat image = imread(image_path);
    if (image.empty()) {
        cout << "Could not load image: " << image_path << endl;
        return false;
    }
    
    // Detect faces
    vector<Rect> faces = detectFaces(image);
    
    if (faces.empty()) {
        cout << "No faces detected in image: " << image_path << endl;
        return false;
    }
    
    cout << "Detected " << faces.size() << " face(s)" << endl;
    
    // Process each face
    for (size_t i = 0; i < faces.size(); ++i) {
        const Rect& face = faces[i];
        
        // Draw face rectangle
        rectangle(image, face, Scalar(0, 255, 0), 2);
        
        // Detect and draw landmarks
        vector<Point2f> landmarks = detectLandmarks(image, face);
        for (const auto& point : landmarks) {
            circle(image, point, 2, Scalar(0, 0, 255), -1);
        }
        
        // Align face
        if (!landmarks.empty()) {
            Mat aligned_face = alignFace(image, landmarks);
            string aligned_path = output_path + "_aligned_" + to_string(i) + ".jpg";
            imwrite(aligned_path, aligned_face);
            cout << "Saved aligned face: " << aligned_path << endl;
        }
    }
    
    // Save annotated image
    imwrite(output_path, image);
    cout << "Saved annotated image: " << output_path << endl;
    
    return true;
}

// 批量处理图像
bool processBatch(const string& input_dir, const string& output_dir, 
                 const FilterConfig& config = {}) {
    if (!filesystem::exists(input_dir)) {
        cout << "Input directory does not exist: " << input_dir << endl;
        return false;
    }
    
    // 创建输出目录
    filesystem::create_directories(output_dir);
    
    // 支持的图像格式
    vector<string> extensions = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif"};
    
    vector<string> image_files;
    for (const auto& entry : filesystem::recursive_directory_iterator(input_dir)) {
        if (entry.is_regular_file()) {
            string ext = entry.path().extension().string();
            transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            
            if (find(extensions.begin(), extensions.end(), ext) != extensions.end()) {
                image_files.push_back(entry.path().string());
            }
        }
    }
    
    if (image_files.empty()) {
        cout << "No image files found in directory: " << input_dir << endl;
        return false;
    }
    
    cout << "Found " << image_files.size() << " image files" << endl;
    
    int success_count = 0;
    int total_count = image_files.size();
    
    auto start_time = high_resolution_clock::now();
    
    for (size_t i = 0; i < image_files.size(); ++i) {
        const string& image_path = image_files[i];
        string filename = filesystem::path(image_path).stem().string();
        string output_path = output_dir + "/" + filename + "_processed.jpg";
        
        cout << "Processing " << (i + 1) << "/" << total_count << ": " << filename << endl;
        
        if (processImage(image_path, output_path, config)) {
            success_count++;
        }
    }
    
    auto end_time = high_resolution_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    cout << "\nBatch processing completed:" << endl;
    cout << "  Processed: " << success_count << "/" << total_count << endl;
    cout << "  Time taken: " << duration.count() << " ms" << endl;
    cout << "  Average time per image: " << (duration.count() / total_count) << " ms" << endl;
    
    return success_count > 0;
}