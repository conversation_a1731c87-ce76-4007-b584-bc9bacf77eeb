#include "BSTFaceUnlock.h"
#include <opencv2/opencv.hpp>
#include <dlib/opencv.h>
#include <dlib/image_processing/frontal_face_detector.h>
#include <dlib/image_processing/render_face_detections.h>
#include <dlib/image_processing.h>
#include <dlib/gui_widgets.h>
#include <dlib/image_io.h>
#include <dlib/dnn.h>
#include <iostream>
#include <vector>
#include <memory>
#include <filesystem>
#include <chrono>
#include <algorithm>

using namespace cv;
using namespace dlib;
using namespace std;
using namespace std::chrono;
using namespace std::filesystem;

void printUsage() {
    cout << "Usage: make_align_data <input_dir> <output_dir> <predictor_path>" << endl;
    cout << "  input_dir     : Directory containing input images" << endl;
    cout << "  output_dir    : Directory for output processed images" << endl;
    cout << "  predictor_path: Path to the shape predictor model file" << endl;
}

int main(int argc, char** argv) {
    if (argc != 4) {
        printUsage();
        return -1;
    }

    string input_dir = argv[1];
    string output_dir = argv[2];
    string predictor_path = argv[3];

    // Create face unlock instance
    BSTFaceUnlock face_unlock;
    
    // Initialize with shape predictor
    if (!face_unlock.initialize(predictor_path)) {
        cout << "Failed to initialize face unlock with predictor: " << predictor_path << endl;
        return -1;
    }

    // Configure processing parameters
    FilterConfig config;
    config.min_face_size = 64;  // Minimum face size to process
    config.max_face_size = 0;   // No maximum size limit

    // Process batch of images
    if (!face_unlock.processBatch(input_dir, output_dir, config)) {
        cout << "Batch processing failed!" << endl;
        return -1;
    }

    return 0;
}

class BSTFaceUnlock::Impl {
    // ... Rest of the implementation remains the same ...
