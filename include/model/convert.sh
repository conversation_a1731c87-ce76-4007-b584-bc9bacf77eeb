#!/bin/bash

# 设置默认的ncnn工具路径
NCNN_TOOL_ROOT="/media/shining/work/work/code/ncnn-20230223/build/tools"
export PATH="${NCNN_TOOL_ROOT}":${PATH}

# 显示使用说明
show_usage() {
    echo "Usage: $0 [-m model_path] [-h]"
    echo "Options:"
    echo "  -m model_path    Specify the model path (without .ncnn extension)"
    echo "  -h              Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -m ./living/V0.8.5/exp47_108_step_516000_A07_dark"
}

# 检查必要的工具是否存在
check_tools() {
    if ! command -v ncnn2mem &> /dev/null; then
        echo "Error: ncnn2mem not found. Please check if ncnn tools are properly installed."
        exit 1
    fi
}

# 检查文件是否存在
check_files() {
    local model_path=$1
    if [ ! -f "${model_path}.param" ] || [ ! -f "${model_path}.bin" ]; then
        echo "Error: Model files not found at ${model_path}"
        exit 1
    fi
}

# 主转换函数
convert_model() {
    local model_path=$1
    echo "Converting model: ${model_path}"
    
    # 检查文件是否存在
    check_files "${model_path}"
    
    # 执行转换
    ncnn2mem "${model_path}.param" "${model_path}.bin" "${model_path}.id.h" "${model_path}.mem.h"
    
    if [ $? -eq 0 ]; then
        echo "Conversion completed successfully!"
        echo "Generated files:"
        echo "  - ${model_path}.id.h"
        echo "  - ${model_path}.mem.h"
    else
        echo "Error: Conversion failed!"
        exit 1
    fi
}

# 解析命令行参数
while getopts "m:h" opt; do
    case $opt in
        m)
            MODEL_PATH="$OPTARG"
            ;;
        h)
            show_usage
            exit 0
            ;;
        \?)
            echo "Invalid option: -$OPTARG" >&2
            show_usage
            exit 1
            ;;
    esac
done

# 检查工具是否可用
check_tools

# 如果没有指定模型路径，使用默认值
if [ -z "$MODEL_PATH" ]; then
    MODEL_PATH="./living/V0.8.5/exp47_108_step_516000_A07_dark"
    echo "No model path specified, using default: ${MODEL_PATH}"
fi

# 执行转换
convert_model "${MODEL_PATH}"

#model_name=V0.7.11/exp474_step_40000_normal.ncnn
# model_name=./blink/V3.10/size_128_v3.10
# $ncnn_tool_root/onnx/onnx2ncnn $model_name.onnx $model_name.param $model_name.bin
# ncnn2mem ${model_name}.param ${model_name}.bin ${model_name}.id.h ${model_name}.mem.h

# /media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn size_128_v1.3.onnx size_128_v1.3.param size_128_v1.3.bin
# ncnn2mem size_128_v1.3.param size_128_v1.3.bin size_128_v1.3.id.h size_128_v1.3.mem.h

# export PATH="/media/shining/work/work/code/ncnn-20230223/build/tools":${PATH}