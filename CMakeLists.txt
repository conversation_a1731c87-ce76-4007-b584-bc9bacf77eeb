cmake_minimum_required (VERSION 3.4)

project("bstFaceUnlock")

# global settings

# version
# ht : host
# ne : normal env - Android/Linux/Darwin
# st : sim-tee
# tg : Teegris
set(VERSION_BASE    		"2")
set(VERSION_MAJOR   		"6")
set(VERSION_MINOR   		"1")
set(VERSION_BUILD   		"1")
set(VERSION_TARGET			"ht") # default
# set(VERSION_MODEL			"1")
# set(DEVICE_TARGET			"universal")
# set(VERSION_MODEL			"4")
# set(DEVICE_TARGET			"U655AA")
# set(VERSION_MODEL			"3")
# set(DEVICE_TARGET			"P811")
# set(VERSION_MODEL			"4")
# set(DEVICE_TARGET			"X9A")
# set(VERSION_MODEL			"5")
# set(DEVICE_TARGET			"W3")
#  set(VERSION_MODEL		"6")
#  set(DEVICE_TARGET		"U572AA")
# set(VERSION_MODEL			"7")
# set(DEVICE_TARGET			"M197")
set(VERSION_MODEL			"110")
set(DEVICE_TARGET			"M196")
# macro
# # macro
if(SECUREOS)
	add_definitions(-DSECUREOS)
endif()

# build target
message(STATUS "host_system:		${CMAKE_SYSTEM_NAME}")
message(STATUS "target_system:		${CMAKE_SYSTEM_NAME}")
message(STATUS "version:		${VERSION_BASE}.${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_BUILD}.{VERSION_MODEL}")
if(SECUREOS)
	set(TARGET_PLATFORM "secureos-arm64-v8a")
else()
	if(ANDROID)
		message(STATUS "target_abi:		${ANDROID_ABI}")
		if(ANDROID_ABI STREQUAL "arm64-v8a")
			if(SIMSECUREOS)
				set(TARGET_PLATFORM "android-sim-secureos-arm64-v8a")
			else()
				set(TARGET_PLATFORM "android-arm64-v8a")
			endif()
		elseif(ANDROID_ABI STREQUAL "armeabi-v7a")
			set(TARGET_PLATFORM "android-armeabi-v7a")
		else()
			message(FATAL_ERROR "unsupported target platform")
		endif()
	elseif(CMAKE_SYSTEM_NAME STREQUAL "Linux")
		set(TARGET_PLATFORM "linux-amd64")
	elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
		set(TARGET_PLATFORM "darwin-arm64")
	else()
		message(FATAL_ERROR "unsupported target platform")
	endif()
endif()
# gcc/ld option
if(TARGET_PLATFORM STREQUAL "linux-amd64")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g  -std=c++11 ")
#	add_definitions("-DUSING_MODEL_FILE")
elseif(TARGET_PLATFORM STREQUAL "darwin-arm64")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}   -O2 -Ofast -std=c++11 -gfull -fsanitize=address")
	# add_definitions("-DUSING_MODEL_FILE")
elseif(TARGET_PLATFORM STREQUAL "secureos-arm64-v8a")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}   -O2 -Ofast \
		-fvisibility-inlines-hidden -fPIC -std=c++11 \
		-fno-use-cxa-atexit -fno-rtti -fno-exceptions ")
else() # android
	# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}   -O2 -Ofast \
	# 	-fvisibility-inlines-hidden -fPIC -std=c++11 \
	# 	-nostdinc++ -fno-builtin -fno-use-cxa-atexit -fno-rtti -fno-exceptions")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}   -O2 -Ofast \
		-fvisibility-inlines-hidden -fPIC -std=c++11 -fopenmp \
		-fno-use-cxa-atexit -fno-rtti -fno-exceptions  -static-openmp")
#	set(CMAKE_SHARED_LINKER_FLAGS   ${CMAKE_SHARED_LINKER_FLAGS} -static-openmp)
endif()

# header/lib path
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn)
if(TARGET_PLATFORM STREQUAL "linux-amd64")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn-platform/linux-amd64)
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/stb)
elseif(TARGET_PLATFORM STREQUAL "darwin-arm64")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn-platform/darwin-arm64)
elseif(TARGET_PLATFORM STREQUAL "android-arm64-v8a")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn-platform/android-arm64-v8a)
	set(VERSION_TARGET	"ne")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/stb)
elseif(TARGET_PLATFORM STREQUAL "android-armeabi-v7a")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn-platform/android-armeabi-v7a)
	set(VERSION_TARGET	"ne")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/stb)
elseif(TARGET_PLATFORM STREQUAL "secureos-arm64-v8a")
	set(VERSION_TARGET	"tg")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn-platform/secureos-arm64-v8a)
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/openlibm)
elseif(TARGET_PLATFORM STREQUAL "android-sim-secureos-arm64-v8a")
	set(VERSION_TARGET	"st")
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/ncnn-platform/android-sim-secureos-arm64-v8a)
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/stb)
else()
	message(FATAL_ERROR "unsupported target platform")
endif()
set(VERSION_TARGET	${DEVICE_TARGET}_${VERSION_TARGET})
if(TARGET_PLATFORM STREQUAL "linux-amd64")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/linux-amd64)
elseif(TARGET_PLATFORM STREQUAL "darwin-arm64")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/darwin-arm64)
elseif(TARGET_PLATFORM STREQUAL "android-arm64-v8a")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/android-arm64-v8a)
elseif(TARGET_PLATFORM STREQUAL "android-armeabi-v7a")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/android-armeabi-v7a)
elseif(TARGET_PLATFORM STREQUAL "android-sim-secureos-arm64-v8a")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/android-sim-secureos-arm64-v8a)
elseif(TARGET_PLATFORM STREQUAL "secureos-arm64-v8a")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/secureos-arm64-v8a)
else()
	message(FATAL_ERROR "unsupported target platform")
endif()

# linked libs
set(STATIC_LINK_LIBS
	infer
)

# import config
configure_file(
	"${CMAKE_CURRENT_SOURCE_DIR}/cmake/Version.h.in"
	"${CMAKE_CURRENT_SOURCE_DIR}/include/Version.h"
)
configure_file(
	"${CMAKE_CURRENT_SOURCE_DIR}/cmake/Version.str.in"
	"${CMAKE_CURRENT_SOURCE_DIR}/cmake/Version.str"
)
configure_file(
	"${CMAKE_CURRENT_SOURCE_DIR}/cmake/DeviceTarget.str.in"
	"${CMAKE_CURRENT_SOURCE_DIR}/cmake/DeviceTarget.str"
)
execute_process(COMMAND xxd -i ./cmake/built_in_cfg.json ./include/BuiltInCfg.h
				WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

add_subdirectory(src)

if(TARGET_PLATFORM STREQUAL "linux-amd64")
	set(STATIC_LINK_LIBS
			BSTFaceAuthTEE
			-pthread
			-fopenmp
			-ldl
			ncnn
	)
	if(TARGET_PLATFORM STREQUAL "android-arm64-v8a" OR TARGET_PLATFORM STREQUAL "android-armeabi-v7a")
		set(DYNAMIC_LINK_LIBS
				log
		)
	else()
		set(DYNAMIC_LINK_LIBS)
	endif()
	add_executable(bstFaceUnlockTest test/demo_folder.cpp)
	target_link_libraries(bstFaceUnlockTest ${STATIC_LINK_LIBS} ${DYNAMIC_LINK_LIBS})
	# build test
	add_executable(demo_folder test/demo_folder.cpp doc/BSTMatch.cpp)
	include_directories("${CMAKE_CURRENT_SOURCE_DIR}/doc")
	target_link_libraries(demo_folder ${STATIC_LINK_LIBS} ${DYNAMIC_LINK_LIBS})
elseif (TARGET_PLATFORM STREQUAL "android-arm64-v8a" OR TARGET_PLATFORM STREQUAL "android-armeabi-v7a")
	set(STATIC_LINK_LIBS
		bstFaceUnlock
		infer
	)
	if(TARGET_PLATFORM STREQUAL "android-arm64-v8a" OR TARGET_PLATFORM STREQUAL "android-armeabi-v7a")
		set(DYNAMIC_LINK_LIBS    
			log
		)
	else()
		set(DYNAMIC_LINK_LIBS)
	endif()
	add_executable(bstFaceUnlockTest test/demo_folder.cpp)
	# target_compile_options(bstFaceUnlockTest PUBLIC -fsanitize=address -fno-omit-frame-pointer)
    # set_target_properties(bstFaceUnlockTest PROPERTIES LINK_FLAGS -fsanitize=address)
	target_link_libraries(bstFaceUnlockTest ${STATIC_LINK_LIBS} ${DYNAMIC_LINK_LIBS})
	# build test
	add_executable(demo_folder test/demo_folder.cpp doc/BSTMatch.cpp)
	include_directories("${CMAKE_CURRENT_SOURCE_DIR}/doc")
	target_link_libraries(demo_folder ${STATIC_LINK_LIBS} ${DYNAMIC_LINK_LIBS})
elseif(TARGET_PLATFORM STREQUAL "darwin-arm64")
	set(STATIC_LINK_LIBS
		BSTFaceAuthTEE
		infer
	)
	add_executable(bstFaceUnlockTest test/demo.cpp)
	target_link_libraries(bstFaceUnlockTest 
		${STATIC_LINK_LIBS} 
		${DYNAMIC_LINK_LIBS}
		"-framework OpenGL"
		"-framework CoreFoundation"
		"-framework Cocoa"
		"-framework IOKit"
		"-framework GLUT"
	)
elseif(TARGET_PLATFORM STREQUAL "secureos-arm64-v8a")
	set(STATIC_LINK_LIBS
		infer
		openlibm
	)
	add_definitions(-DMAIN_MOCK)
	# add_executable(bstFaceUnlockTest test/demo.cpp)
	# target_link_libraries(bstFaceUnlockTest BSTFaceAuthTEE ${STATIC_LINK_LIBS})
else()
	if(BUILD_TEST)
		# tee build check - mock main func
		add_definitions(-DMAIN_MOCK)
		add_executable(bstFaceUnlockTest test/demo.cpp)
		target_link_libraries(bstFaceUnlockTest bstFaceUnlock ${STATIC_LINK_LIBS})
	endif()
endif()