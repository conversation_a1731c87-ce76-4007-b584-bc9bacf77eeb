#include "model/ModuleLandmarker.h"
#include "CommonUtils.h"

#ifndef USING_MODEL_FILE
#include "model/Models.h"
#endif

#include "simpleocv.h"

int ModuleLandmarker::init() {
    LOGI("ModuleLandmarker init");
    int ret = 0;
#ifndef USING_MODEL_FILE
    // TODO: add param
    Models models_ncnn;
    landmark_net.load_param(models_ncnn.pipfacelmk_param_bin);
    landmark_net.load_model(models_ncnn.pipfacelmk_bin);

#else // TODO: add param
    ret = landmark_net.load_param("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/landmarker/pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur.param");
    ret = landmark_net.load_model("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/landmarker/pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur.bin");
#endif
    return ret;
}

int ModuleLandmarker::release() {
    landmark_net.clear();

    return 0;
}

void getoutput(ncnn::Extractor ex, int blob_idx, std::vector<float*>& outputs, std::vector<std::vector<int> >& out_shapes) {
    ncnn::Mat out_hm;
    int ret = ex.extract(blob_idx, out_hm);

    float* out_hm_ptr = new float[out_hm.d * out_hm.c * out_hm.h * out_hm.w];
    if (NULL == out_hm_ptr) return ;
    bstutils::pretty_copy(out_hm, out_hm_ptr);
    outputs.push_back(out_hm_ptr);
    std::vector<int> tmp;
    tmp.push_back(out_hm.d);
    tmp.push_back(out_hm.c);
    tmp.push_back(out_hm.h);
    tmp.push_back(out_hm.w);
    out_shapes.push_back(tmp);
}

bool ModuleLandmarker::perfom(unsigned char* img, int img_w, int img_h, int c, const BBox& bbox, LmkInfo& info) {
    if (img == NULL || img_w <= 0 || img_h <= 0 || c != net_in_c_) {
        LOGI("ModuleLandmarker input image data error, img=%p,img_w=%d,h=%d,c=%d", (void*)img, img_w, img_h, c);
        return false;
    }

    //extend face bbox
    int extend_x, extend_y, extend_w, extend_h;
    int cx = bbox.x + bbox.w * 0.5;
    int cy = bbox.y + bbox.h * 0.5;

    float max_wd_ht = std::max(bbox.w, bbox.h);
    float ratio     = 1.3;
    max_wd_ht *= ratio;

    extend_x      = cx - max_wd_ht / 2;
    extend_y      = cy - max_wd_ht / 2;
    extend_w      = max_wd_ht;
    extend_h      = max_wd_ht;
    int extend_x3 = cx + max_wd_ht / 2;
    int extend_y3 = cy + max_wd_ht / 2;

    // out of bound check
    if (extend_x < 0)
        extend_x = 0;
    if (extend_y < 0)
        extend_y = 0;
    if (extend_x + extend_w >= img_w)
        extend_w = img_w - extend_x - 1;
    if (extend_y + extend_h >= img_h)
        extend_h = img_h - extend_y - 1;
    extend_x3 = std::min(extend_x3, img_w - 1);
    extend_y3 = std::min(extend_y3, img_h - 1);
    extend_w  = extend_x3 - extend_x;
    extend_h  = extend_y3 - extend_y;

    ncnn::Mat net_in = ncnn::Mat::from_pixels_roi_resize((unsigned char*)img, ncnn::Mat::PIXEL_RGB, img_w, img_h, extend_x, extend_y, extend_w, extend_h, net_in_w_, net_in_h_);


#ifdef DUMP_LANDMARK
#define DUMP_LANDMARK
    bstutils::save_image_png("net_in", net_in, ncnn::Mat::PIXEL_RGB);
#endif
    net_in.substract_mean_normalize(this->mean, this->scale);

    /// 1. landmark with attr
    ncnn::Extractor ex = landmark_net.create_extractor();
    ex.set_light_mode(true);
    //    ex.set_num_threads(8);
    ex.input(0, net_in);

    std::vector<float*>            p_outs;
    std::vector<std::vector<int> > out_shapes;
    // for (std::string& name : this->model_.outnames)
    int indexes_size = 8;
    // int indexes[6]   = {78, 80, 82, 84,86, 89}; // output index
    //   hm offset_x offset_y vis pose_y pose_p pose_r attr uncertain
    // int indexes[8]   = {77, 79, 81, 83,92, 96,100, 102}; // output index
    int indexes[8]   = {72, 73, 74, 75,82,84,86,87}; // output index
                                                         //    int indexes[6]   = {73, 74, 75, 76,78, 80}; // output index
    //   hm offset_x offset_y vis pose attr uncertain
    for (int i = 0; i < indexes_size; ++i) {
        getoutput(ex, indexes[i], p_outs, out_shapes);
    }

    if (!decode(p_outs, out_shapes, info,indexes_size)) {
        LOGE("BSTLandmarkerPIP postprocess error");
        for (int i = 0; i < indexes_size; ++i) {
            delete[] p_outs[i];
        }
        return false;
    }
    float scale_w = extend_w * 1.0f / net_in_w_;
    float scale_h = extend_h * 1.0f / net_in_h_;
    for (unsigned int i = 0; i < info.pts.size(); i++) {
        info.pts[i].x = info.pts[i].x * scale_w + extend_x;
        info.pts[i].y = info.pts[i].y * scale_h + extend_y;
    }

    for (int i = 0; i < indexes_size; ++i) {
        delete[] p_outs[i];
    }
    return true;
}

bool ModuleLandmarker::decode(std::vector<float*>& p_outs, std::vector<std::vector<int> >& out_shapes, LmkInfo& lmk_info,int indexes_size) {
    if (p_outs.size() != indexes_size || out_shapes.size() != indexes_size) {
        LOGI("ModuleLandmarker out num != 8");
        return false;
    }

    /*
    hm                  s=(1,7,7,106)
    offset_x            s=(1,7,7,106)
    offset_y            s=(1,7,7,106)
    vis                 s=(1,7,7,106)
    pose                s=(1,1,1,3)
    uncertain           s=(1,1,1,1)

     hm                  s=(1，5,8,8,106)
    offset_x            s=(1,5，8,8,106)
    offset_y            s=(1,5，8,8,106)
    vis                 s=(1,5，8,8,106)
    pose                s=(1,，1,1,3)
    uncertain           s=(1,51,1,1)

    */


    float* p_hm        = p_outs[0];
    float* p_offset_x  = p_outs[1];
    float* p_offset_y  = p_outs[2];
    float* p_vis       = p_outs[3];
    float* p_pose_y    = p_outs[4];
    float* p_pose_p    = p_outs[5];
    float* p_pose_r    = p_outs[6];
    float* p_attr_attr = p_outs[7];
    //    float* p_uncertain = p_outs[6];
    lmk_info.pts.resize(this->num_lmk_);
    lmk_info.probs.resize(this->num_lmk_, 0.);
    // lmk_info.probs.assign(this->num_lmk_, 0.0f);
    lmk_info.poses.resize(3, 0.);
    // lmk_info.poses.assign(3, 0.0f);
    lmk_info.vis_probs.resize(this->num_lmk_, 0.);
    // lmk_info.vis_probs.assign(this->num_lmk_, 0.0f);

    int feat_w, feat_h, feat_size;
    feat_w    = this->net_in_w_ / this->stride_;
    feat_h    = this->net_in_h_ / this->stride_;
    feat_size = feat_w * feat_h;
    std::vector<float> hm_sum(this->num_lmk_, 0.000001f);

    if (this->format == 0) { /// DataFormat::NCHW
        int data_idx = 0;
        for (int lmk_idx = 0; lmk_idx < this->num_lmk_; lmk_idx++) {
            int max_prob_idx = 0;
            for (int i = 0; i < feat_h; i++) {
                for (int j = 0; j < feat_w; j++) {
                    data_idx = j + i * feat_w + lmk_idx * feat_h * feat_w;
                    hm_sum[lmk_idx] += p_hm[data_idx];

                    if (p_hm[data_idx] >= lmk_info.probs[lmk_idx]) {
                        lmk_info.probs[lmk_idx] = p_hm[data_idx];
                        max_prob_idx            = data_idx;
                    }
                }
            }
            lmk_info.vis_probs[lmk_idx] = p_vis[max_prob_idx] * lmk_info.probs[lmk_idx];
        }

        float sum_x = 0.0f;
        float sum_y = 0.0f;

        for (int lmk_idx = 0; lmk_idx < this->num_lmk_; lmk_idx++) {
            sum_x = 0.0f;
            sum_y = 0.0f;
            for (int i = 0; i < feat_h; i++) {
                for (int j = 0; j < feat_w; j++) {
                    data_idx = j + i * feat_w + lmk_idx * feat_h * feat_w;
                    p_hm[data_idx] /= hm_sum[lmk_idx];

                    sum_x += ((p_offset_x[data_idx] - 0.5) * this->offset_scale_ + j) * p_hm[data_idx];
                    sum_y += ((p_offset_y[data_idx] - 0.5) * this->offset_scale_ + i) * p_hm[data_idx];
                }
            }

            lmk_info.pts[lmk_idx].x = sum_x * this->stride_;
            lmk_info.pts[lmk_idx].y = sum_y * this->stride_;
        }

        // for (int i = 0; i < 3; i++) {
        //     lmk_info.poses[i] = p_pose[i] * 180;
        // }
        // pose_y
        float angel_sum = 0;
        float angle_step = 180. / out_shapes[4][1];
        /* code */
        for (size_t i = 0; i < out_shapes[4][1]; i++)
        {
            /* code */
            angel_sum += p_pose_y[i]*i;
        }
        lmk_info.poses[0] = angel_sum * angle_step -90;
        angel_sum = 0;
        for (size_t i = 0; i < out_shapes[5][1]; i++)
        {
            /* code */
            angel_sum += p_pose_p[i]*i;
        }
        lmk_info.poses[1] = angel_sum * angle_step -90;
        angel_sum = 0;
        for (size_t i = 0; i < out_shapes[6][1]; i++)
        {
            /* code */
            angel_sum += p_pose_r[i]*i;
        }
        lmk_info.poses[2] = angel_sum * angle_step -90;
        //        lmk_info.uncertain = p_uncertain[0];
        lmk_info.mask_prob = (p_attr_attr[1]);
        lmk_info.blur_prob = (p_attr_attr[0]);
        //        LOGI("mask prob:%3f",lmk_info.mask_prob);
        //        LOGI("blur_prob:%3f",lmk_info.blur_prob);
    }

    return true;
}

