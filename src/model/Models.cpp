#include "model/Models.h"
#include "Version.h"
#include "model/detector/v0.7/size_192_v0.7.mem.h"

//#include "model/blink/V2/size_128_v0.1.mem.h"
// #include "model/blink/V3.4/size_128_v0.17.mem.h"
#include "model/blink/V3.8/size_128_v1.8.mem.h"

/// normal model
#if defined(SN509L)
#include "model/living/V0.5.24/exp402_step_172352_eer_val_normal0.9249_val_dark0.7182_val_U6550.9262_val_U6550.9262_val_SN509A0.9789_val_W30.9649_val_SL219A0.9932acc_0.924858_sim.mem.h"
#elif defined(X9A)
#include "model/living/V0.5.23/exp402_step_280000_eer_val_normal0.9071_val_dark0.7452_val_U6550.8778_val_SN509A0.8414_val_W30.9525_val_SL219A0.9192acc_0.907123_sim.mem.h"
//#include "model/living/V0.5.22/exp410_step_400000_eer_val_normal0.9051_val_dark0.7294_val_U6550.9050_val_SN509A0.8917_val_W30.9689_val_SL219A0.8182acc_0.905085_sim.mem.h"
#elif defined(W3)
// #include "model/living/V0.5.26/exp422_step_25014_eer_O22_val_normal0.9061_O22_val_dark0.7460_val_U655_V20.8238_W3_val_dark0.8689_W3_val_normal0.8937acc_0.906148_sim.mem.h"
//#include "model/living/V0.5.26/exp423_step_16000_eer_O22_val_normal0.9497_O22_val_dark0.7804_val_U655_V20.8249_W3_val_dark0.8416_W3_val_normal0.9122acc_0.949694_sim.mem.h"
//#include "model/living/V0.5.27/exp428_step_48000_eer_O22_val_normal0.9219_O22_val_dark0.7170_val_U655_V20.8733_W3_val_dark0.8609_W3_val_normal0.9318acc_0.921910_sim.mem.h"
//#include "model/living/V0.5.27/exp428_step_96000_eer_O22_val_normal0.9086_O22_val_dark0.7301_val_U655_V20.8847_W3_val_dark0.8271_W3_val_normal0.9135acc_0.908577_sim.mem.h"
//#include "model/living/V0.5.27/step_376000_eer_O22_val_normal:0.9342_O22_val_dark:0.7441_val_U655_V2:0.8714_W3_val_dark:0.8719_W3_val_normal:0.9314acc_0.934206_sim.mem.h"


//#include "model/living/V0.5.28/exp210_step_12000_eer_weak+kaggleMaskReal0.8394_weak0.8802_hard0.8963_eval_haibo0.9226_multiframe_3frame0.8615acc_0.839387_sim.mem.h"
//#include "model/living/V0.5.28/exp210_step_56000_eer_weak+kaggleMaskReal0.7706_weak0.8590_hard0.9186_eval_haibo0.8505_multiframe_3frame0.8725acc_0.770593_sim.mem.h"
/// dark
#include "model/living/V0.5.28/step_332000_eer_val_items_O220.8205_val_normal0.9155_val_dark0.7220_ce_camera0.8601acc_0.820546_sim.mem.h"
///normal
#include "model/living/V0.5.28/step_516000_eer_val_items_O220.8091_val_normal0.9299_val_dark0.6943_ce_camera0.8317acc_0.809143_sim.mem.h"
#elif defined(M197)

#include "model/living/V0.7.1/exp247_step_232000.ncnn.mem.h"

#include "model/living/V0.7.7/exp308_step_140000.ncnn.mem.h"

#elif defined(M196)

#include "model/living/V0.10.3/exp8_step_100000_M196_normal.ncnn.mem.h"

#include "model/living/V0.10.3/exp8_step_216000_M196_dark.ncnn.mem.h"


#elif defined(U655AA) || defined(P811) || defined(U572AA)
// #include "model/living/V0.5.19/exp402_step_440000_eer_val_normal0.8932_val_dark0.7263_val_U6550.9136_val_SN509A0.8931_val_W30.9579_val_SL219A0.9162acc_0.893180_sim.mem.h"
// #include "model/living/V0.5.19/exp402_step_416000_eer_val_normal0.9234_val_dark0.7110_val_U6550.9108_val_SN509A0.9145_val_W30.9542_val_SL219A0.9165acc_0.923410_sim.mem.h"
// #include "model/living/V0.5.19/exp408_step_72000_eer_val_normal0.9241_val_dark0.6668_val_U6550.9069_val_SN509A0.8906_val_W30.9616_val_SL219A0.9197acc_0.924056_sim.mem.h"
//#include "model/living/V0.5.15/exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.mem.h"
#include "model/living/V0.5.25/exp416_step_171696_eer_val_normal0.8983_val_dark0.7146_val_U655_V20.8722_val_SN509A_V20.8877_val_W3_V20.9562acc_0.898313_sim.mem.h"
//#elif defined(U572AA)
//#include "model/living/V0.5.21/exp408_step_256000_eer_val_normal0.9037_val_dark0.6956_val_U6550.9193_val_SN509A0.9044_val_W30.9596_val_SL219A0.8972acc_0.903705_sim.mem.h"
#else // default
#include "model/living/V0.5.11/exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.mem.h"
/// dark model
#include "model/living/V0.5.11/exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.mem.h"
#endif

//// paper_screen_detect
// #include "model/screen_paper_detector/V0.7/screen_paper_v0.7.mem.h"
#include "model/screen_paper_detector/V0.13/screen_paper_yolov8.ncnn.mem.h"
// #include "model/recognizer/V1.1/arcface_webface42m_epoch_49.mem.h"
#include "model/recognizer/V1.3/wf42m_pfc02_8gpus_T_mbflarge_S_mbflarge10_distill_v2_exp192_model_fix_pnnx_ncnn.mem.h"
// #include "model/recognizer/V1.4/wf42m_pfc02_8gpus_T_mbflarge_S_mbflarge05_distill_v4_exp174_model_fix_pnnx_ncnn.mem.h"

#include "model/landmarker/V2.3/face_landmark_5p_attr_blur_mask_epoch_285_loss_2.611498.mem.h"
/// nose
#include "model/nose/V0.1/nose_mouth_size128.mem.h"

Models::Models() :
      model_num(7),
      detect_param_bin(size_192_v0_7_param_bin),
      detect_bin(size_192_v0_7_bin),
      nose_param_bin(nose_mouth_size128_param_bin),
      nose_bin(nose_mouth_size128_bin),
      paper_screen_detect_param_bin(screen_paper_yolov8_ncnn_param_bin),
      paper_screen_detect_bin(screen_paper_yolov8_ncnn_bin),
      pipfacelmk_param_bin(face_landmark_5p_attr_blur_mask_epoch_285_loss_2_611498_param_bin),
      pipfacelmk_bin(face_landmark_5p_attr_blur_mask_epoch_285_loss_2_611498_bin),


//    normal_living_param_bin(exp402_step_440000_eer_val_normal0_8932_val_dark0_7263_val_U6550_9136_val_SN509A0_8931_val_W30_9579_val_SL219A0_9162acc_0_893180_sim_param_bin),
//    normal_living_bin(exp402_step_440000_eer_val_normal0_8932_val_dark0_7263_val_U6550_9136_val_SN509A0_8931_val_W30_9579_val_SL219A0_9162acc_0_893180_sim_bin),
//      normal_living_param_bin(exp402_step_264000_eer_val_normal0_9120_val_dark0_7255_val_U6550_8906_val_SN509A0_9156_val_W30_9507_val_SL219A0_9213acc_0_911992_sim_param_bin),
//      normal_living_bin(exp402_step_264000_eer_val_normal0_9120_val_dark0_7255_val_U6550_8906_val_SN509A0_9156_val_W30_9507_val_SL219A0_9213acc_0_911992_sim_bin),
//            normal_living_param_bin(exp402_step_416000_eer_val_normal0_9234_val_dark0_7110_val_U6550_9108_val_SN509A0_9145_val_W30_9542_val_SL219A0_9165acc_0_923410_sim_param_bin),
//            normal_living_bin(exp402_step_416000_eer_val_normal0_9234_val_dark0_7110_val_U6550_9108_val_SN509A0_9145_val_W30_9542_val_SL219A0_9165acc_0_923410_sim_bin),
//      normal_living_param_bin(exp402_step_416000_eer_val_normal0_9234_val_dark0_7110_val_U6550_9108_val_SN509A0_9145_val_W30_9542_val_SL219A0_9165acc_0_923410_sim_param_bin),
//      normal_living_bin(exp402_step_416000_eer_val_normal0_9234_val_dark0_7110_val_U6550_9108_val_SN509A0_9145_val_W30_9542_val_SL219A0_9165acc_0_923410_sim_bin),
//      normal_living_param_bin(exp408_step_64000_eer_val_normal0_9045_val_dark0_7356_val_U6550_8726_val_SN509A0_9197_val_W30_9373_val_SL219A0_8946acc_0_904483_sim_param_bin),
//      normal_living_bin(exp408_step_64000_eer_val_normal0_9045_val_dark0_7356_val_U6550_8726_val_SN509A0_9197_val_W30_9373_val_SL219A0_8946acc_0_904483_sim_bin),
//      normal_living_param_bin(exp408_step_72000_eer_val_normal0_9241_val_dark0_6668_val_U6550_9069_val_SN509A0_8906_val_W30_9616_val_SL219A0_9197acc_0_924056_sim_param_bin),
//      normal_living_bin(exp408_step_72000_eer_val_normal0_9241_val_dark0_6668_val_U6550_9069_val_SN509A0_8906_val_W30_9616_val_SL219A0_9197acc_0_924056_sim_bin),
//      normal_living_param_bin(exp408_step_80000_eer_val_normal0_9275_val_dark0_7038_val_U6550_8879_val_SN509A0_9209_val_W30_9477_val_SL219A0_8531acc_0_927495_sim_param_bin),
//      normal_living_bin(exp408_step_80000_eer_val_normal0_9275_val_dark0_7038_val_U6550_8879_val_SN509A0_9209_val_W30_9477_val_SL219A0_8531acc_0_927495_sim_bin),
//      normal_living_param_bin(exp402_step_368000_eer_val_normal0_9246_val_dark0_7332_val_U6550_8955_val_SN509A0_8904_val_W30_9528_val_SL219A0_9229acc_0_924562_sim_param_bin),
//      normal_living_bin(exp402_step_368000_eer_val_normal0_9246_val_dark0_7332_val_U6550_8955_val_SN509A0_8904_val_W30_9528_val_SL219A0_9229acc_0_924562_sim_bin),
//      normal_living_param_bin(exp402_step_215440_eer_val_normal0_9362_val_dark0_6786_val_U6550_8944_val_SN509A0_9188_val_W30_9616_val_SL219A0_9273acc_0_936179_sim_param_bin),
//      normal_living_bin(exp402_step_215440_eer_val_normal0_9362_val_dark0_6786_val_U6550_8944_val_SN509A0_9188_val_W30_9616_val_SL219A0_9273acc_0_936179_sim_bin),
//    normal_living_param_bin(exp416_step_56000_eer_val_normal0_9000_val_dark0_7095_val_U655_V20_8441_val_SN509A_V20_8807_val_W3_V20_9407acc_0_900019_sim_param_bin),
//    normal_living_bin(exp416_step_56000_eer_val_normal0_9000_val_dark0_7095_val_U655_V20_8441_val_SN509A_V20_8807_val_W3_V20_9407acc_0_900019_sim_bin),
//          normal_living_param_bin(exp410_step_400000_eer_val_normal0_9051_val_dark0_7294_val_U6550_9050_val_SN509A0_8917_val_W30_9689_val_SL219A0_8182acc_0_905085_sim_param_bin),
//          normal_living_bin(exp410_step_400000_eer_val_normal0_9051_val_dark0_7294_val_U6550_9050_val_SN509A0_8917_val_W30_9689_val_SL219A0_8182acc_0_905085_sim_bin),
#if defined(SN509L)
      normal_living_param_bin(exp402_step_172352_eer_val_normal0_9249_val_dark0_7182_val_U6550_9262_val_U6550_9262_val_SN509A0_9789_val_W30_9649_val_SL219A0_9932acc_0_924858_sim_param_bin),
      normal_living_bin(exp402_step_172352_eer_val_normal0_9249_val_dark0_7182_val_U6550_9262_val_U6550_9262_val_SN509A0_9789_val_W30_9649_val_SL219A0_9932acc_0_924858_sim_bin),
      dark_living_param_bin(exp334_step_64000_eer_val_items_O220_8696_val_normal0_9081_val_dark0_8350_ce_camera0_8665acc_0_869586_sim_param_bin),
      dark_living_bin(exp334_step_64000_eer_val_items_O220_8696_val_normal0_9081_val_dark0_8350_ce_camera0_8665acc_0_869586_sim_bin),

#elif defined(X9A)
      normal_living_param_bin(exp402_step_280000_eer_val_normal0_9071_val_dark0_7452_val_U6550_8778_val_SN509A0_8414_val_W30_9525_val_SL219A0_9192acc_0_907123_sim_param_bin),
      normal_living_bin(exp402_step_280000_eer_val_normal0_9071_val_dark0_7452_val_U6550_8778_val_SN509A0_8414_val_W30_9525_val_SL219A0_9192acc_0_907123_sim_bin),
      dark_living_param_bin(exp334_step_64000_eer_val_items_O220_8696_val_normal0_9081_val_dark0_8350_ce_camera0_8665acc_0_869586_sim_param_bin),
      dark_living_bin(exp334_step_64000_eer_val_items_O220_8696_val_normal0_9081_val_dark0_8350_ce_camera0_8665acc_0_869586_sim_bin),

#elif defined(W3)
//      normal_living_param_bin(exp423_step_16000_eer_O22_val_normal0_9497_O22_val_dark0_7804_val_U655_V20_8249_W3_val_dark0_8416_W3_val_normal0_9122acc_0_949694_sim_param_bin),
//      normal_living_bin(exp423_step_16000_eer_O22_val_normal0_9497_O22_val_dark0_7804_val_U655_V20_8249_W3_val_dark0_8416_W3_val_normal0_9122acc_0_949694_sim_bin),
////      dark_living_param_bin(exp423_step_16000_eer_O22_val_normal0_9497_O22_val_dark0_7804_val_U655_V20_8249_W3_val_dark0_8416_W3_val_normal0_9122acc_0_949694_sim_param_bin),
////      dark_living_bin(exp423_step_16000_eer_O22_val_normal0_9497_O22_val_dark0_7804_val_U655_V20_8249_W3_val_dark0_8416_W3_val_normal0_9122acc_0_949694_sim_bin),
//      dark_living_param_bin(exp422_step_25014_eer_O22_val_normal0_9061_O22_val_dark0_7460_val_U655_V20_8238_W3_val_dark0_8689_W3_val_normal0_8937acc_0_906148_sim_param_bin),
//      dark_living_bin(exp422_step_25014_eer_O22_val_normal0_9061_O22_val_dark0_7460_val_U655_V20_8238_W3_val_dark0_8689_W3_val_normal0_8937acc_0_906148_sim_bin),

      normal_living_param_bin(step_516000_eer_val_items_O220_8091_val_normal0_9299_val_dark0_6943_ce_camera0_8317acc_0_809143_sim_param_bin),
      normal_living_bin(step_516000_eer_val_items_O220_8091_val_normal0_9299_val_dark0_6943_ce_camera0_8317acc_0_809143_sim_bin),

      //      normal_living_param_bin(exp425_step_216000_eer_O22_val_normal0_9425_O22_val_dark0_7076_val_U655_V20_8341_W3_val_dark0_8563_W3_val_normal0_9322acc_0_942496_sim_param_bin),
//      normal_living_bin(exp425_step_216000_eer_O22_val_normal0_9425_O22_val_dark0_7076_val_U655_V20_8341_W3_val_dark0_8563_W3_val_normal0_9322acc_0_942496_sim_bin),

        dark_living_param_bin(step_332000_eer_val_items_O220_8205_val_normal0_9155_val_dark0_7220_ce_camera0_8601acc_0_820546_sim_param_bin),
        dark_living_bin(step_332000_eer_val_items_O220_8205_val_normal0_9155_val_dark0_7220_ce_camera0_8601acc_0_820546_sim_bin),
//        big_angel_living_param_bin(exp428_step_96000_eer_O22_val_normal0_9086_O22_val_dark0_7301_val_U655_V20_8847_W3_val_dark0_8271_W3_val_normal0_9135acc_0_908577_sim_param_bin),
//        big_angel_living_bin(exp428_step_96000_eer_O22_val_normal0_9086_O22_val_dark0_7301_val_U655_V20_8847_W3_val_dark0_8271_W3_val_normal0_9135acc_0_908577_sim_bin),

#elif defined(M197)
      // normal_living_param_bin(exp268_step_1188000_eer_val_items_kaggleMaskReal_cecamera_0_7791acc_0_779094_sim_param_bin),
      // normal_living_bin(exp268_step_1188000_eer_val_items_kaggleMaskReal_cecamera_0_7791acc_0_779094_sim_bin),
      // normal_living_param_bin(exp257_step_248000_param_bin),
      // normal_living_bin(exp257_step_248000_bin),
      // dark_living_param_bin(exp306_step_1720000_eer_val_items_O22_0_8122_ce_camera_0_8623acc_0_812215_sim_param_bin),
      // dark_living_bin(exp306_step_1720000_eer_val_items_O22_0_8122_ce_camera_0_8623acc_0_812215_sim_bin),

      //   dark_living_param_bin(exp306_step_1720000_param_bin),
      //   dark_living_bin(exp306_step_1720000_bin),
        dark_living_param_bin(exp247_step_232000_ncnn_param_bin),
        dark_living_bin(exp247_step_232000_ncnn_bin),
      // normal_living_param_bin(exp257_step_576000_param_bin),
      // normal_living_bin(exp257_step_576000_bin),
      // normal_living_param_bin(exp268_step_1738100_param_bin),
      // normal_living_bin(exp268_step_1738100_bin),
      normal_living_param_bin(exp308_step_140000_ncnn_param_bin),
      normal_living_bin(exp308_step_140000_ncnn_bin),
#elif defined(M196)

      dark_living_param_bin(exp8_step_216000_M196_dark_ncnn_param_bin),
      dark_living_bin(exp8_step_216000_M196_dark_ncnn_bin),
      normal_living_param_bin(exp8_step_100000_M196_normal_ncnn_param_bin),
      normal_living_bin(exp8_step_100000_M196_normal_ncnn_bin),
#elif defined(U655AA)  || defined(P811) || defined(U572AA)
      //      normal_living_param_bin(exp388_step_144000_eer_val_normal0_9490_val_dark0_7648acc_0_949013_sim_param_bin),
      //      normal_living_bin(exp388_step_144000_eer_val_normal0_9490_val_dark0_7648acc_0_949013_sim_bin),
      normal_living_param_bin(exp416_step_171696_eer_val_normal0_8983_val_dark0_7146_val_U655_V20_8722_val_SN509A_V20_8877_val_W3_V20_9562acc_0_898313_sim_param_bin),
      normal_living_bin(exp416_step_171696_eer_val_normal0_8983_val_dark0_7146_val_U655_V20_8722_val_SN509A_V20_8877_val_W3_V20_9562acc_0_898313_sim_bin),
//#elif defined(U572AA)
//      normal_living_param_bin(exp408_step_256000_eer_val_normal0_9037_val_dark0_6956_val_U6550_9193_val_SN509A0_9044_val_W30_9596_val_SL219A0_8972acc_0_903705_sim_param_bin),
//      normal_living_bin(exp408_step_256000_eer_val_normal0_9037_val_dark0_6956_val_U6550_9193_val_SN509A0_9044_val_W30_9596_val_SL219A0_8972acc_0_903705_sim_bin),
#else
      normal_living_param_bin(exp339_step_556000_eer_val_normal0_9659_val_dark0_6968acc_0_965896_sim_param_bin),
      normal_living_bin(exp339_step_556000_eer_val_normal0_9659_val_dark0_6968acc_0_965896_sim_bin),
      dark_living_param_bin(exp334_step_64000_eer_val_items_O220_8696_val_normal0_9081_val_dark0_8350_ce_camera0_8665acc_0_869586_sim_param_bin),
      dark_living_bin(exp334_step_64000_eer_val_items_O220_8696_val_normal0_9081_val_dark0_8350_ce_camera0_8665acc_0_869586_sim_bin),

#endif
      blink_param_bin(size_128_v1_8_param_bin),
      blink_bin(size_128_v1_8_bin),
      recognizer_param_bin(wf42m_pfc02_8gpus_T_mbflarge_S_mbflarge10_distill_v2_exp192_model_fix_pnnx_ncnn_param_bin),
      recognizer_bin(wf42m_pfc02_8gpus_T_mbflarge_S_mbflarge10_distill_v2_exp192_model_fix_pnnx_ncnn_bin) {
}